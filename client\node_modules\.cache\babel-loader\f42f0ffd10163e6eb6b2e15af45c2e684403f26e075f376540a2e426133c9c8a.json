{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ZeroWaste\\\\client\\\\src\\\\components\\\\FirebaseStatus.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FirebaseStatus = () => {\n  const config = {\n    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,\n    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,\n    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,\n    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,\n    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,\n    appId: process.env.REACT_APP_FIREBASE_APP_ID,\n    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID\n  };\n  const configItems = [{\n    key: 'API Key',\n    value: config.apiKey,\n    required: true\n  }, {\n    key: 'Auth Domain',\n    value: config.authDomain,\n    required: true\n  }, {\n    key: 'Project ID',\n    value: config.projectId,\n    required: true\n  }, {\n    key: 'Storage Bucket',\n    value: config.storageBucket,\n    required: true\n  }, {\n    key: 'Messaging Sender ID',\n    value: config.messagingSenderId,\n    required: true\n  }, {\n    key: 'App ID',\n    value: config.appId,\n    required: true\n  }, {\n    key: 'Measurement ID',\n    value: config.measurementId,\n    required: false\n  }];\n  const allRequired = configItems.filter(item => item.required).every(item => item.value);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white border rounded-lg p-4 mb-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-semibold mb-4 flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mr-2\",\n        children: \"\\uD83D\\uDD27\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), \"Firebase Configuration Status\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-2\",\n      children: configItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-600\",\n          children: [item.key, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-sm font-medium ${item.value ? 'text-green-600' : item.required ? 'text-red-600' : 'text-yellow-600'}`,\n          children: item.value ? '✓ Configured' : item.required ? '✗ Missing' : '⚠ Optional'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this)]\n      }, item.key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `mt-4 p-3 rounded-lg ${allRequired ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `text-sm font-medium ${allRequired ? 'text-green-800' : 'text-red-800'}`,\n        children: allRequired ? '✅ Firebase configuration is complete' : '❌ Firebase configuration is incomplete'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), !allRequired && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-700 text-xs mt-1\",\n        children: \"Please check your .env file and ensure all required Firebase configuration values are set.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), allRequired && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-blue-800 text-sm font-medium\",\n        children: \"\\uD83D\\uDCCB Next Steps for Google Sign-In:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n        className: \"text-blue-700 text-xs mt-1 list-decimal list-inside space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Go to Firebase Console \\u2192 Authentication \\u2192 Sign-in method\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Enable Google provider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Add your support email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Ensure localhost is in authorized domains\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_c = FirebaseStatus;\nexport default FirebaseStatus;\nvar _c;\n$RefreshReg$(_c, \"FirebaseStatus\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FirebaseStatus", "config", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_FIREBASE_API_KEY", "authDomain", "REACT_APP_FIREBASE_AUTH_DOMAIN", "projectId", "REACT_APP_FIREBASE_PROJECT_ID", "storageBucket", "REACT_APP_FIREBASE_STORAGE_BUCKET", "messagingSenderId", "REACT_APP_FIREBASE_MESSAGING_SENDER_ID", "appId", "REACT_APP_FIREBASE_APP_ID", "measurementId", "REACT_APP_FIREBASE_MEASUREMENT_ID", "configItems", "key", "value", "required", "allRequired", "filter", "item", "every", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ZeroWaste/client/src/components/FirebaseStatus.js"], "sourcesContent": ["import React from 'react';\n\nconst FirebaseStatus = () => {\n  const config = {\n    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,\n    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,\n    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,\n    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,\n    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,\n    appId: process.env.REACT_APP_FIREBASE_APP_ID,\n    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID\n  };\n\n  const configItems = [\n    { key: 'API Key', value: config.apiKey, required: true },\n    { key: 'Auth Domain', value: config.authDomain, required: true },\n    { key: 'Project ID', value: config.projectId, required: true },\n    { key: 'Storage Bucket', value: config.storageBucket, required: true },\n    { key: 'Messaging Sender ID', value: config.messagingSenderId, required: true },\n    { key: 'App ID', value: config.appId, required: true },\n    { key: 'Measurement ID', value: config.measurementId, required: false },\n  ];\n\n  const allRequired = configItems.filter(item => item.required).every(item => item.value);\n\n  return (\n    <div className=\"bg-white border rounded-lg p-4 mb-6\">\n      <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n        <span className=\"mr-2\">🔧</span>\n        Firebase Configuration Status\n      </h3>\n      \n      <div className=\"space-y-2\">\n        {configItems.map((item) => (\n          <div key={item.key} className=\"flex justify-between items-center\">\n            <span className=\"text-sm text-gray-600\">{item.key}:</span>\n            <span className={`text-sm font-medium ${\n              item.value \n                ? 'text-green-600' \n                : item.required \n                  ? 'text-red-600' \n                  : 'text-yellow-600'\n            }`}>\n              {item.value ? '✓ Configured' : item.required ? '✗ Missing' : '⚠ Optional'}\n            </span>\n          </div>\n        ))}\n      </div>\n\n      <div className={`mt-4 p-3 rounded-lg ${\n        allRequired \n          ? 'bg-green-50 border border-green-200' \n          : 'bg-red-50 border border-red-200'\n      }`}>\n        <div className={`text-sm font-medium ${\n          allRequired ? 'text-green-800' : 'text-red-800'\n        }`}>\n          {allRequired \n            ? '✅ Firebase configuration is complete' \n            : '❌ Firebase configuration is incomplete'\n          }\n        </div>\n        {!allRequired && (\n          <div className=\"text-red-700 text-xs mt-1\">\n            Please check your .env file and ensure all required Firebase configuration values are set.\n          </div>\n        )}\n      </div>\n\n      {allRequired && (\n        <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n          <div className=\"text-blue-800 text-sm font-medium\">\n            📋 Next Steps for Google Sign-In:\n          </div>\n          <ol className=\"text-blue-700 text-xs mt-1 list-decimal list-inside space-y-1\">\n            <li>Go to Firebase Console → Authentication → Sign-in method</li>\n            <li>Enable Google provider</li>\n            <li>Add your support email</li>\n            <li>Ensure localhost is in authorized domains</li>\n          </ol>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FirebaseStatus;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,MAAM,GAAG;IACbC,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B;IAC9CC,UAAU,EAAEH,OAAO,CAACC,GAAG,CAACG,8BAA8B;IACtDC,SAAS,EAAEL,OAAO,CAACC,GAAG,CAACK,6BAA6B;IACpDC,aAAa,EAAEP,OAAO,CAACC,GAAG,CAACO,iCAAiC;IAC5DC,iBAAiB,EAAET,OAAO,CAACC,GAAG,CAACS,sCAAsC;IACrEC,KAAK,EAAEX,OAAO,CAACC,GAAG,CAACW,yBAAyB;IAC5CC,aAAa,EAAEb,OAAO,CAACC,GAAG,CAACa;EAC7B,CAAC;EAED,MAAMC,WAAW,GAAG,CAClB;IAAEC,GAAG,EAAE,SAAS;IAAEC,KAAK,EAAEnB,MAAM,CAACC,MAAM;IAAEmB,QAAQ,EAAE;EAAK,CAAC,EACxD;IAAEF,GAAG,EAAE,aAAa;IAAEC,KAAK,EAAEnB,MAAM,CAACK,UAAU;IAAEe,QAAQ,EAAE;EAAK,CAAC,EAChE;IAAEF,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAEnB,MAAM,CAACO,SAAS;IAAEa,QAAQ,EAAE;EAAK,CAAC,EAC9D;IAAEF,GAAG,EAAE,gBAAgB;IAAEC,KAAK,EAAEnB,MAAM,CAACS,aAAa;IAAEW,QAAQ,EAAE;EAAK,CAAC,EACtE;IAAEF,GAAG,EAAE,qBAAqB;IAAEC,KAAK,EAAEnB,MAAM,CAACW,iBAAiB;IAAES,QAAQ,EAAE;EAAK,CAAC,EAC/E;IAAEF,GAAG,EAAE,QAAQ;IAAEC,KAAK,EAAEnB,MAAM,CAACa,KAAK;IAAEO,QAAQ,EAAE;EAAK,CAAC,EACtD;IAAEF,GAAG,EAAE,gBAAgB;IAAEC,KAAK,EAAEnB,MAAM,CAACe,aAAa;IAAEK,QAAQ,EAAE;EAAM,CAAC,CACxE;EAED,MAAMC,WAAW,GAAGJ,WAAW,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACH,QAAQ,CAAC,CAACI,KAAK,CAACD,IAAI,IAAIA,IAAI,CAACJ,KAAK,CAAC;EAEvF,oBACErB,OAAA;IAAK2B,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAClD5B,OAAA;MAAI2B,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAC1D5B,OAAA;QAAM2B,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,iCAElC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAELhC,OAAA;MAAK2B,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBT,WAAW,CAACc,GAAG,CAAER,IAAI,iBACpBzB,OAAA;QAAoB2B,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC/D5B,OAAA;UAAM2B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GAAEH,IAAI,CAACL,GAAG,EAAC,GAAC;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DhC,OAAA;UAAM2B,SAAS,EAAE,uBACfF,IAAI,CAACJ,KAAK,GACN,gBAAgB,GAChBI,IAAI,CAACH,QAAQ,GACX,cAAc,GACd,iBAAiB,EACtB;UAAAM,QAAA,EACAH,IAAI,CAACJ,KAAK,GAAG,cAAc,GAAGI,IAAI,CAACH,QAAQ,GAAG,WAAW,GAAG;QAAY;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC;MAAA,GAVCP,IAAI,CAACL,GAAG;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWb,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENhC,OAAA;MAAK2B,SAAS,EAAE,uBACdJ,WAAW,GACP,qCAAqC,GACrC,iCAAiC,EACpC;MAAAK,QAAA,gBACD5B,OAAA;QAAK2B,SAAS,EAAE,uBACdJ,WAAW,GAAG,gBAAgB,GAAG,cAAc,EAC9C;QAAAK,QAAA,EACAL,WAAW,GACR,sCAAsC,GACtC;MAAwC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEzC,CAAC,EACL,CAACT,WAAW,iBACXvB,OAAA;QAAK2B,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAE3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELT,WAAW,iBACVvB,OAAA;MAAK2B,SAAS,EAAC,uDAAuD;MAAAC,QAAA,gBACpE5B,OAAA;QAAK2B,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAEnD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNhC,OAAA;QAAI2B,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC3E5B,OAAA;UAAA4B,QAAA,EAAI;QAAwD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEhC,OAAA;UAAA4B,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BhC,OAAA;UAAA4B,QAAA,EAAI;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/BhC,OAAA;UAAA4B,QAAA,EAAI;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACE,EAAA,GAlFIjC,cAAc;AAoFpB,eAAeA,cAAc;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { signInWithPopup, signOut, onAuthStateChanged, GoogleAuthProvider } from 'firebase/auth';\nimport { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';\nimport { auth, db, googleProvider } from './firebase';\nimport { requestNotificationPermission } from './firebase';\n\n// Enhanced Google provider configuration\ngoogleProvider.setCustomParameters({\n  prompt: 'select_account'\n});\n\n// Sign in with Google\nexport const signInWithGoogle = async selectedRole => {\n  try {\n    if (!selectedRole) {\n      throw new Error('Please select a role before signing in');\n    }\n    const result = await signInWithPopup(auth, googleProvider);\n    const user = result.user;\n\n    // Check if user already exists in Firestore\n    const userDocRef = doc(db, 'users', user.uid);\n    const userDoc = await getDoc(userDocRef);\n    if (!userDoc.exists()) {\n      // Request notification permission and get FCM token\n      const fcmToken = await requestNotificationPermission();\n\n      // Create new user document\n      await setDoc(userDocRef, {\n        uid: user.uid,\n        email: user.email,\n        displayName: user.displayName,\n        photoURL: user.photoURL,\n        role: selectedRole,\n        createdAt: serverTimestamp(),\n        lastLoginAt: serverTimestamp(),\n        fcmToken: fcmToken,\n        preferences: {\n          notifications: true,\n          emailUpdates: false\n        },\n        stats: {\n          foodListed: 0,\n          foodClaimed: 0,\n          impactScore: 0\n        }\n      });\n      console.log('New user created:', user.uid);\n    } else {\n      // Update existing user's last login and FCM token\n      const fcmToken = await requestNotificationPermission();\n      await updateDoc(userDocRef, {\n        lastLoginAt: serverTimestamp(),\n        fcmToken: fcmToken\n      });\n      console.log('Existing user signed in:', user.uid);\n    }\n    return {\n      success: true,\n      user: user,\n      isNewUser: !userDoc.exists()\n    };\n  } catch (error) {\n    console.error('Error signing in with Google:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Sign out\nexport const signOutUser = async () => {\n  try {\n    await signOut(auth);\n    console.log('User signed out successfully');\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error signing out:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Get current user data from Firestore\nexport const getCurrentUserData = async uid => {\n  try {\n    const userDocRef = doc(db, 'users', uid);\n    const userDoc = await getDoc(userDocRef);\n    if (userDoc.exists()) {\n      return {\n        success: true,\n        userData: userDoc.data()\n      };\n    } else {\n      return {\n        success: false,\n        error: 'User data not found'\n      };\n    }\n  } catch (error) {\n    console.error('Error getting user data:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Update user profile\nexport const updateUserProfile = async (uid, updates) => {\n  try {\n    const userDocRef = doc(db, 'users', uid);\n    await updateDoc(userDocRef, {\n      ...updates,\n      updatedAt: serverTimestamp()\n    });\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error updating user profile:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Auth state observer\nexport const onAuthStateChange = callback => {\n  return onAuthStateChanged(auth, callback);\n};\n\n// Check if user has specific role\nexport const hasRole = async (uid, requiredRole) => {\n  try {\n    const userData = await getCurrentUserData(uid);\n    if (userData.success) {\n      return userData.userData.role === requiredRole;\n    }\n    return false;\n  } catch (error) {\n    console.error('Error checking user role:', error);\n    return false;\n  }\n};\n\n// Check if user can list food (canteen staff, admin)\nexport const canListFood = async uid => {\n  try {\n    const userData = await getCurrentUserData(uid);\n    if (userData.success) {\n      const role = userData.userData.role;\n      return role === 'canteen_staff' || role === 'admin';\n    }\n    return false;\n  } catch (error) {\n    console.error('Error checking food listing permission:', error);\n    return false;\n  }\n};", "map": {"version": 3, "names": ["signInWithPopup", "signOut", "onAuthStateChanged", "GoogleAuthProvider", "doc", "setDoc", "getDoc", "updateDoc", "serverTimestamp", "auth", "db", "googleProvider", "requestNotificationPermission", "setCustomParameters", "prompt", "signInWithGoogle", "selectedR<PERSON>", "Error", "result", "user", "userDocRef", "uid", "userDoc", "exists", "fcmToken", "email", "displayName", "photoURL", "role", "createdAt", "lastLoginAt", "preferences", "notifications", "emailUpdates", "stats", "foodListed", "foodClaimed", "impactScore", "console", "log", "success", "isNewUser", "error", "message", "signOutUser", "getCurrentUserData", "userData", "data", "updateUserProfile", "updates", "updatedAt", "onAuthStateChange", "callback", "hasRole", "requiredRole", "canListFood"], "sources": ["C:/Users/<USER>/Desktop/ZeroWaste/client/src/services/authService.js"], "sourcesContent": ["import { \n  signInWithPopup, \n  signOut, \n  onAuthStateChanged,\n  GoogleAuthProvider \n} from 'firebase/auth';\nimport { \n  doc, \n  setDoc, \n  getDoc, \n  updateDoc, \n  serverTimestamp \n} from 'firebase/firestore';\nimport { auth, db, googleProvider } from './firebase';\nimport { requestNotificationPermission } from './firebase';\n\n// Enhanced Google provider configuration\ngoogleProvider.setCustomParameters({\n  prompt: 'select_account'\n});\n\n// Sign in with Google\nexport const signInWithGoogle = async (selectedRole) => {\n  try {\n    if (!selectedRole) {\n      throw new Error('Please select a role before signing in');\n    }\n\n    const result = await signInWithPopup(auth, googleProvider);\n    const user = result.user;\n\n    // Check if user already exists in Firestore\n    const userDocRef = doc(db, 'users', user.uid);\n    const userDoc = await getDoc(userDocRef);\n\n    if (!userDoc.exists()) {\n      // Request notification permission and get FCM token\n      const fcmToken = await requestNotificationPermission();\n\n      // Create new user document\n      await setDoc(userDocRef, {\n        uid: user.uid,\n        email: user.email,\n        displayName: user.displayName,\n        photoURL: user.photoURL,\n        role: selectedRole,\n        createdAt: serverTimestamp(),\n        lastLoginAt: serverTimestamp(),\n        fcmToken: fcmToken,\n        preferences: {\n          notifications: true,\n          emailUpdates: false,\n        },\n        stats: {\n          foodListed: 0,\n          foodClaimed: 0,\n          impactScore: 0,\n        }\n      });\n\n      console.log('New user created:', user.uid);\n    } else {\n      // Update existing user's last login and FCM token\n      const fcmToken = await requestNotificationPermission();\n      await updateDoc(userDocRef, {\n        lastLoginAt: serverTimestamp(),\n        fcmToken: fcmToken,\n      });\n\n      console.log('Existing user signed in:', user.uid);\n    }\n\n    return {\n      success: true,\n      user: user,\n      isNewUser: !userDoc.exists()\n    };\n  } catch (error) {\n    console.error('Error signing in with Google:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Sign out\nexport const signOutUser = async () => {\n  try {\n    await signOut(auth);\n    console.log('User signed out successfully');\n    return { success: true };\n  } catch (error) {\n    console.error('Error signing out:', error);\n    return { success: false, error: error.message };\n  }\n};\n\n// Get current user data from Firestore\nexport const getCurrentUserData = async (uid) => {\n  try {\n    const userDocRef = doc(db, 'users', uid);\n    const userDoc = await getDoc(userDocRef);\n    \n    if (userDoc.exists()) {\n      return {\n        success: true,\n        userData: userDoc.data()\n      };\n    } else {\n      return {\n        success: false,\n        error: 'User data not found'\n      };\n    }\n  } catch (error) {\n    console.error('Error getting user data:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Update user profile\nexport const updateUserProfile = async (uid, updates) => {\n  try {\n    const userDocRef = doc(db, 'users', uid);\n    await updateDoc(userDocRef, {\n      ...updates,\n      updatedAt: serverTimestamp()\n    });\n    \n    return { success: true };\n  } catch (error) {\n    console.error('Error updating user profile:', error);\n    return { success: false, error: error.message };\n  }\n};\n\n// Auth state observer\nexport const onAuthStateChange = (callback) => {\n  return onAuthStateChanged(auth, callback);\n};\n\n// Check if user has specific role\nexport const hasRole = async (uid, requiredRole) => {\n  try {\n    const userData = await getCurrentUserData(uid);\n    if (userData.success) {\n      return userData.userData.role === requiredRole;\n    }\n    return false;\n  } catch (error) {\n    console.error('Error checking user role:', error);\n    return false;\n  }\n};\n\n// Check if user can list food (canteen staff, admin)\nexport const canListFood = async (uid) => {\n  try {\n    const userData = await getCurrentUserData(uid);\n    if (userData.success) {\n      const role = userData.userData.role;\n      return role === 'canteen_staff' || role === 'admin';\n    }\n    return false;\n  } catch (error) {\n    console.error('Error checking food listing permission:', error);\n    return false;\n  }\n};\n"], "mappings": "AAAA,SACEA,eAAe,EACfC,OAAO,EACPC,kBAAkB,EAClBC,kBAAkB,QACb,eAAe;AACtB,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,eAAe,QACV,oBAAoB;AAC3B,SAASC,IAAI,EAAEC,EAAE,EAAEC,cAAc,QAAQ,YAAY;AACrD,SAASC,6BAA6B,QAAQ,YAAY;;AAE1D;AACAD,cAAc,CAACE,mBAAmB,CAAC;EACjCC,MAAM,EAAE;AACV,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,gBAAgB,GAAG,MAAOC,YAAY,IAAK;EACtD,IAAI;IACF,IAAI,CAACA,YAAY,EAAE;MACjB,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IAEA,MAAMC,MAAM,GAAG,MAAMlB,eAAe,CAACS,IAAI,EAAEE,cAAc,CAAC;IAC1D,MAAMQ,IAAI,GAAGD,MAAM,CAACC,IAAI;;IAExB;IACA,MAAMC,UAAU,GAAGhB,GAAG,CAACM,EAAE,EAAE,OAAO,EAAES,IAAI,CAACE,GAAG,CAAC;IAC7C,MAAMC,OAAO,GAAG,MAAMhB,MAAM,CAACc,UAAU,CAAC;IAExC,IAAI,CAACE,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;MACrB;MACA,MAAMC,QAAQ,GAAG,MAAMZ,6BAA6B,CAAC,CAAC;;MAEtD;MACA,MAAMP,MAAM,CAACe,UAAU,EAAE;QACvBC,GAAG,EAAEF,IAAI,CAACE,GAAG;QACbI,KAAK,EAAEN,IAAI,CAACM,KAAK;QACjBC,WAAW,EAAEP,IAAI,CAACO,WAAW;QAC7BC,QAAQ,EAAER,IAAI,CAACQ,QAAQ;QACvBC,IAAI,EAAEZ,YAAY;QAClBa,SAAS,EAAErB,eAAe,CAAC,CAAC;QAC5BsB,WAAW,EAAEtB,eAAe,CAAC,CAAC;QAC9BgB,QAAQ,EAAEA,QAAQ;QAClBO,WAAW,EAAE;UACXC,aAAa,EAAE,IAAI;UACnBC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLC,UAAU,EAAE,CAAC;UACbC,WAAW,EAAE,CAAC;UACdC,WAAW,EAAE;QACf;MACF,CAAC,CAAC;MAEFC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEpB,IAAI,CAACE,GAAG,CAAC;IAC5C,CAAC,MAAM;MACL;MACA,MAAMG,QAAQ,GAAG,MAAMZ,6BAA6B,CAAC,CAAC;MACtD,MAAML,SAAS,CAACa,UAAU,EAAE;QAC1BU,WAAW,EAAEtB,eAAe,CAAC,CAAC;QAC9BgB,QAAQ,EAAEA;MACZ,CAAC,CAAC;MAEFc,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEpB,IAAI,CAACE,GAAG,CAAC;IACnD;IAEA,OAAO;MACLmB,OAAO,EAAE,IAAI;MACbrB,IAAI,EAAEA,IAAI;MACVsB,SAAS,EAAE,CAACnB,OAAO,CAACC,MAAM,CAAC;IAC7B,CAAC;EACH,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO;MACLF,OAAO,EAAE,KAAK;MACdE,KAAK,EAAEA,KAAK,CAACC;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAM3C,OAAO,CAACQ,IAAI,CAAC;IACnB6B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3C,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC1C,OAAO;MAAEF,OAAO,EAAE,KAAK;MAAEE,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAME,kBAAkB,GAAG,MAAOxB,GAAG,IAAK;EAC/C,IAAI;IACF,MAAMD,UAAU,GAAGhB,GAAG,CAACM,EAAE,EAAE,OAAO,EAAEW,GAAG,CAAC;IACxC,MAAMC,OAAO,GAAG,MAAMhB,MAAM,CAACc,UAAU,CAAC;IAExC,IAAIE,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;MACpB,OAAO;QACLiB,OAAO,EAAE,IAAI;QACbM,QAAQ,EAAExB,OAAO,CAACyB,IAAI,CAAC;MACzB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLP,OAAO,EAAE,KAAK;QACdE,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO;MACLF,OAAO,EAAE,KAAK;MACdE,KAAK,EAAEA,KAAK,CAACC;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAO3B,GAAG,EAAE4B,OAAO,KAAK;EACvD,IAAI;IACF,MAAM7B,UAAU,GAAGhB,GAAG,CAACM,EAAE,EAAE,OAAO,EAAEW,GAAG,CAAC;IACxC,MAAMd,SAAS,CAACa,UAAU,EAAE;MAC1B,GAAG6B,OAAO;MACVC,SAAS,EAAE1C,eAAe,CAAC;IAC7B,CAAC,CAAC;IAEF,OAAO;MAAEgC,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO;MAAEF,OAAO,EAAE,KAAK;MAAEE,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,iBAAiB,GAAIC,QAAQ,IAAK;EAC7C,OAAOlD,kBAAkB,CAACO,IAAI,EAAE2C,QAAQ,CAAC;AAC3C,CAAC;;AAED;AACA,OAAO,MAAMC,OAAO,GAAG,MAAAA,CAAOhC,GAAG,EAAEiC,YAAY,KAAK;EAClD,IAAI;IACF,MAAMR,QAAQ,GAAG,MAAMD,kBAAkB,CAACxB,GAAG,CAAC;IAC9C,IAAIyB,QAAQ,CAACN,OAAO,EAAE;MACpB,OAAOM,QAAQ,CAACA,QAAQ,CAAClB,IAAI,KAAK0B,YAAY;IAChD;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMa,WAAW,GAAG,MAAOlC,GAAG,IAAK;EACxC,IAAI;IACF,MAAMyB,QAAQ,GAAG,MAAMD,kBAAkB,CAACxB,GAAG,CAAC;IAC9C,IAAIyB,QAAQ,CAACN,OAAO,EAAE;MACpB,MAAMZ,IAAI,GAAGkB,QAAQ,CAACA,QAAQ,CAAClB,IAAI;MACnC,OAAOA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,OAAO;IACrD;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOc,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IAC/D,OAAO,KAAK;EACd;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
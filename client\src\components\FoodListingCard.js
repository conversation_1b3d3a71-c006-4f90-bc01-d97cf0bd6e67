import React, { useState } from 'react';
import { claimFoodListing } from '../services/foodService';

const FoodListingCard = ({ listing, currentUser }) => {
  const [loading, setLoading] = useState(false);

  const getStatusColor = (status) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'claimed':
        return 'bg-yellow-100 text-yellow-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getFreshnessColor = (freshness) => {
    switch (freshness) {
      case 'fresh':
        return 'text-green-600';
      case 'good':
        return 'text-yellow-600';
      case 'consume_soon':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatTimeRemaining = (expiryTime) => {
    const now = new Date();
    const expiry = expiryTime.toDate ? expiryTime.toDate() : new Date(expiryTime);
    const diffMs = expiry - now;
    
    if (diffMs <= 0) {
      return 'Expired';
    }
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m remaining`;
    } else {
      return `${diffMinutes}m remaining`;
    }
  };

  const handleClaimFood = async () => {
    if (!currentUser || listing.status !== 'available') return;

    setLoading(true);
    try {
      const result = await claimFoodListing(listing.id, currentUser);

      if (result.success) {
        alert('Food claimed successfully! Please contact the lister for pickup details.');
      } else {
        alert(`Error claiming food: ${result.error}`);
      }
    } catch (error) {
      console.error('Error claiming food:', error);
      alert('Error claiming food. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const canClaim = currentUser && 
                  listing.status === 'available' && 
                  listing.listedBy.uid !== currentUser.uid;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-200">
      {/* Image */}
      {listing.imageUrl && (
        <div className="h-48 bg-gray-200">
          <img 
            src={listing.imageUrl} 
            alt={listing.name}
            className="w-full h-full object-cover"
          />
        </div>
      )}
      
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-3">
          <h3 className="text-xl font-semibold text-gray-900">{listing.name}</h3>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(listing.status)}`}>
            {listing.status.charAt(0).toUpperCase() + listing.status.slice(1)}
          </span>
        </div>

        {/* Description */}
        {listing.description && (
          <p className="text-gray-600 mb-3 text-sm">{listing.description}</p>
        )}

        {/* Details */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Quantity:</span>
            <span className="font-medium">{listing.quantity} {listing.unit}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Freshness:</span>
            <span className={`font-medium ${getFreshnessColor(listing.freshnessStatus)}`}>
              {listing.freshnessStatus.replace('_', ' ').charAt(0).toUpperCase() + 
               listing.freshnessStatus.replace('_', ' ').slice(1)}
            </span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Location:</span>
            <span className="font-medium">{listing.location}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Time Remaining:</span>
            <span className={`font-medium ${
              listing.status === 'expired' ? 'text-red-600' : 'text-gray-900'
            }`}>
              {formatTimeRemaining(listing.expiryTime)}
            </span>
          </div>

          {listing.allergens && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Allergens/Diet:</span>
              <span className="font-medium text-right">{listing.allergens}</span>
            </div>
          )}
        </div>

        {/* Listed By */}
        <div className="text-xs text-gray-500 mb-4">
          Listed by {listing.listedBy.name}
        </div>

        {/* Claimed By Info */}
        {listing.status === 'claimed' && listing.claimedBy && (
          <div className="bg-yellow-50 p-3 rounded-lg mb-4">
            <p className="text-sm text-yellow-800">
              Claimed by {listing.claimedBy.name}
            </p>
          </div>
        )}

        {/* Contact Info */}
        {listing.contactInfo && listing.status === 'claimed' && 
         listing.claimedBy && listing.claimedBy.uid === currentUser?.uid && (
          <div className="bg-blue-50 p-3 rounded-lg mb-4">
            <p className="text-sm text-blue-800">
              <strong>Contact:</strong> {listing.contactInfo}
            </p>
          </div>
        )}

        {/* Action Button */}
        {canClaim && (
          <button
            onClick={handleClaimFood}
            disabled={loading}
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Claiming...
              </div>
            ) : (
              'Claim Food'
            )}
          </button>
        )}

        {listing.listedBy.uid === currentUser?.uid && (
          <div className="text-center text-sm text-gray-500">
            Your listing
          </div>
        )}
      </div>
    </div>
  );
};

export default FoodListingCard;

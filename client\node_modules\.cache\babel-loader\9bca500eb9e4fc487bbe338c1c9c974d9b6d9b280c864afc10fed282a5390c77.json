{"ast": null, "code": "import { collection, doc, setDoc } from 'firebase/firestore';\nimport { db } from './firebase';\n\n// Initialize Firestore collections with sample data and proper structure\nexport const initializeFirestore = async () => {\n  try {\n    console.log('Initializing Firestore collections...');\n\n    // Create sample food listing\n    const sampleFoodListing = {\n      name: 'Vegetable Biryani',\n      description: 'Freshly prepared vegetable biryani with aromatic spices',\n      quantity: 20,\n      unit: 'servings',\n      freshnessStatus: 'fresh',\n      expiryHours: 4,\n      expiryTime: new Date(Date.now() + 4 * 60 * 60 * 1000),\n      // 4 hours from now\n      location: 'Main Canteen',\n      contactInfo: '+91 9876543210',\n      allergens: 'Vegetarian, Contains nuts',\n      imageUrl: null,\n      status: 'available',\n      listedBy: {\n        uid: 'sample-user-id',\n        name: 'Main Canteen Staff',\n        email: '<EMAIL>'\n      },\n      claimedBy: null,\n      claimedAt: null,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    };\n\n    // Add sample food listing\n    await setDoc(doc(collection(db, 'foodListings'), 'sample-listing'), sampleFoodListing);\n\n    // Create sample user document structure\n    const sampleUser = {\n      uid: 'sample-user-id',\n      email: '<EMAIL>',\n      displayName: 'Main Canteen Staff',\n      photoURL: null,\n      role: 'canteen_staff',\n      createdAt: new Date(),\n      fcmToken: null,\n      preferences: {\n        notifications: true,\n        emailUpdates: false\n      },\n      stats: {\n        foodListed: 1,\n        foodClaimed: 0,\n        impactScore: 100\n      }\n    };\n\n    // Add sample user\n    await setDoc(doc(collection(db, 'users'), 'sample-user-id'), sampleUser);\n\n    // Create sample impact data\n    const sampleImpact = {\n      totalFoodSaved: 1250,\n      totalPeopleServed: 500,\n      co2Saved: 2.5,\n      // in tons\n      waterSaved: 1200,\n      // in liters\n      lastUpdated: new Date()\n    };\n\n    // Add sample impact data\n    await setDoc(doc(collection(db, 'impact'), 'global-stats'), sampleImpact);\n    console.log('Firestore collections initialized successfully!');\n    return true;\n  } catch (error) {\n    console.error('Error initializing Firestore:', error);\n    return false;\n  }\n};\n\n// Firestore Security Rules (to be added in Firebase Console)\nexport const firestoreSecurityRules = `\nrules_version = '2';\nservice cloud.firestore {\n  match /databases/{database}/documents {\n    // Users can read and write their own user document\n    match /users/{userId} {\n      allow read, write: if request.auth != null && request.auth.uid == userId;\n      allow read: if request.auth != null; // Allow reading other users for display purposes\n    }\n    \n    // Food listings rules\n    match /foodListings/{listingId} {\n      allow read: if request.auth != null;\n      allow create: if request.auth != null && \n                   request.auth.uid == resource.data.listedBy.uid;\n      allow update: if request.auth != null && (\n        // Owner can update their listing\n        request.auth.uid == resource.data.listedBy.uid ||\n        // Users can claim food (update claimedBy and status)\n        (resource.data.status == 'available' && \n         request.resource.data.status == 'claimed' &&\n         request.resource.data.claimedBy.uid == request.auth.uid)\n      );\n      allow delete: if request.auth != null && \n                   request.auth.uid == resource.data.listedBy.uid;\n    }\n    \n    // Impact data - read only for users, write for cloud functions\n    match /impact/{document} {\n      allow read: if request.auth != null;\n      allow write: if false; // Only cloud functions can write\n    }\n    \n    // Claims collection for tracking\n    match /claims/{claimId} {\n      allow read, write: if request.auth != null && (\n        request.auth.uid == resource.data.claimedBy.uid ||\n        request.auth.uid == resource.data.listedBy.uid\n      );\n    }\n  }\n}\n`;", "map": {"version": 3, "names": ["collection", "doc", "setDoc", "db", "initializeFirestore", "console", "log", "sampleFoodListing", "name", "description", "quantity", "unit", "freshnessStatus", "expiryHours", "expiryTime", "Date", "now", "location", "contactInfo", "allergens", "imageUrl", "status", "listedBy", "uid", "email", "claimed<PERSON>y", "claimedAt", "createdAt", "updatedAt", "sampleUser", "displayName", "photoURL", "role", "fcmToken", "preferences", "notifications", "emailUpdates", "stats", "foodListed", "foodClaimed", "impactScore", "sampleImpact", "totalFoodSaved", "totalPeopleServed", "co2Saved", "waterSaved", "lastUpdated", "error", "firestoreSecurityRules"], "sources": ["C:/Users/<USER>/Desktop/ZeroWaste/client/src/services/firestoreSetup.js"], "sourcesContent": ["import { collection, doc, setDoc } from 'firebase/firestore';\nimport { db } from './firebase';\n\n// Initialize Firestore collections with sample data and proper structure\nexport const initializeFirestore = async () => {\n  try {\n    console.log('Initializing Firestore collections...');\n\n    // Create sample food listing\n    const sampleFoodListing = {\n      name: 'Vegetable Biryani',\n      description: 'Freshly prepared vegetable biryani with aromatic spices',\n      quantity: 20,\n      unit: 'servings',\n      freshnessStatus: 'fresh',\n      expiryHours: 4,\n      expiryTime: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now\n      location: 'Main Canteen',\n      contactInfo: '+91 9876543210',\n      allergens: 'Vegetarian, Contains nuts',\n      imageUrl: null,\n      status: 'available',\n      listedBy: {\n        uid: 'sample-user-id',\n        name: 'Main Canteen Staff',\n        email: '<EMAIL>',\n      },\n      claimedBy: null,\n      claimedAt: null,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    // Add sample food listing\n    await setDoc(doc(collection(db, 'foodListings'), 'sample-listing'), sampleFoodListing);\n\n    // Create sample user document structure\n    const sampleUser = {\n      uid: 'sample-user-id',\n      email: '<EMAIL>',\n      displayName: 'Main Canteen Staff',\n      photoURL: null,\n      role: 'canteen_staff',\n      createdAt: new Date(),\n      fcmToken: null,\n      preferences: {\n        notifications: true,\n        emailUpdates: false,\n      },\n      stats: {\n        foodListed: 1,\n        foodClaimed: 0,\n        impactScore: 100,\n      }\n    };\n\n    // Add sample user\n    await setDoc(doc(collection(db, 'users'), 'sample-user-id'), sampleUser);\n\n    // Create sample impact data\n    const sampleImpact = {\n      totalFoodSaved: 1250,\n      totalPeopleServed: 500,\n      co2Saved: 2.5, // in tons\n      waterSaved: 1200, // in liters\n      lastUpdated: new Date(),\n    };\n\n    // Add sample impact data\n    await setDoc(doc(collection(db, 'impact'), 'global-stats'), sampleImpact);\n\n    console.log('Firestore collections initialized successfully!');\n    return true;\n  } catch (error) {\n    console.error('Error initializing Firestore:', error);\n    return false;\n  }\n};\n\n// Firestore Security Rules (to be added in Firebase Console)\nexport const firestoreSecurityRules = `\nrules_version = '2';\nservice cloud.firestore {\n  match /databases/{database}/documents {\n    // Users can read and write their own user document\n    match /users/{userId} {\n      allow read, write: if request.auth != null && request.auth.uid == userId;\n      allow read: if request.auth != null; // Allow reading other users for display purposes\n    }\n    \n    // Food listings rules\n    match /foodListings/{listingId} {\n      allow read: if request.auth != null;\n      allow create: if request.auth != null && \n                   request.auth.uid == resource.data.listedBy.uid;\n      allow update: if request.auth != null && (\n        // Owner can update their listing\n        request.auth.uid == resource.data.listedBy.uid ||\n        // Users can claim food (update claimedBy and status)\n        (resource.data.status == 'available' && \n         request.resource.data.status == 'claimed' &&\n         request.resource.data.claimedBy.uid == request.auth.uid)\n      );\n      allow delete: if request.auth != null && \n                   request.auth.uid == resource.data.listedBy.uid;\n    }\n    \n    // Impact data - read only for users, write for cloud functions\n    match /impact/{document} {\n      allow read: if request.auth != null;\n      allow write: if false; // Only cloud functions can write\n    }\n    \n    // Claims collection for tracking\n    match /claims/{claimId} {\n      allow read, write: if request.auth != null && (\n        request.auth.uid == resource.data.claimedBy.uid ||\n        request.auth.uid == resource.data.listedBy.uid\n      );\n    }\n  }\n}\n`;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,GAAG,EAAEC,MAAM,QAAQ,oBAAoB;AAC5D,SAASC,EAAE,QAAQ,YAAY;;AAE/B;AACA,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EAC7C,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;IAEpD;IACA,MAAMC,iBAAiB,GAAG;MACxBC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,yDAAyD;MACtEC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,UAAU;MAChBC,eAAe,EAAE,OAAO;MACxBC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACvDC,QAAQ,EAAE,cAAc;MACxBC,WAAW,EAAE,gBAAgB;MAC7BC,SAAS,EAAE,2BAA2B;MACtCC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,WAAW;MACnBC,QAAQ,EAAE;QACRC,GAAG,EAAE,gBAAgB;QACrBf,IAAI,EAAE,oBAAoB;QAC1BgB,KAAK,EAAE;MACT,CAAC;MACDC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAIZ,IAAI,CAAC,CAAC;MACrBa,SAAS,EAAE,IAAIb,IAAI,CAAC;IACtB,CAAC;;IAED;IACA,MAAMb,MAAM,CAACD,GAAG,CAACD,UAAU,CAACG,EAAE,EAAE,cAAc,CAAC,EAAE,gBAAgB,CAAC,EAAEI,iBAAiB,CAAC;;IAEtF;IACA,MAAMsB,UAAU,GAAG;MACjBN,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE,wBAAwB;MAC/BM,WAAW,EAAE,oBAAoB;MACjCC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,eAAe;MACrBL,SAAS,EAAE,IAAIZ,IAAI,CAAC,CAAC;MACrBkB,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;QACXC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE;MAChB,CAAC;MACDC,KAAK,EAAE;QACLC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE;MACf;IACF,CAAC;;IAED;IACA,MAAMtC,MAAM,CAACD,GAAG,CAACD,UAAU,CAACG,EAAE,EAAE,OAAO,CAAC,EAAE,gBAAgB,CAAC,EAAE0B,UAAU,CAAC;;IAExE;IACA,MAAMY,YAAY,GAAG;MACnBC,cAAc,EAAE,IAAI;MACpBC,iBAAiB,EAAE,GAAG;MACtBC,QAAQ,EAAE,GAAG;MAAE;MACfC,UAAU,EAAE,IAAI;MAAE;MAClBC,WAAW,EAAE,IAAI/B,IAAI,CAAC;IACxB,CAAC;;IAED;IACA,MAAMb,MAAM,CAACD,GAAG,CAACD,UAAU,CAACG,EAAE,EAAE,QAAQ,CAAC,EAAE,cAAc,CAAC,EAAEsC,YAAY,CAAC;IAEzEpC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9D,OAAO,IAAI;EACb,CAAC,CAAC,OAAOyC,KAAK,EAAE;IACd1C,OAAO,CAAC0C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,sBAAsB,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
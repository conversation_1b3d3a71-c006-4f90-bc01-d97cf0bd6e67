import React, { useState, useEffect } from 'react';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from '../services/firebase';
import { subscribeFoodListings } from '../services/foodService';
import FoodListingCard from '../components/FoodListingCard';
import LoadingSpinner from '../components/LoadingSpinner';
import InitializeData from '../components/InitializeData';

const Dashboard = () => {
  const [user] = useAuthState(auth);
  const [foodListings, setFoodListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, available, claimed, expired

  useEffect(() => {
    if (!user) return;

    setLoading(true);

    // Set up filters for the subscription
    const filters = {};
    if (filter !== 'all') {
      filters.status = filter;
    }

    // Subscribe to food listings with real-time updates
    const unsubscribe = subscribeFoodListings((listings) => {
      setFoodListings(listings);
      setLoading(false);
    }, filters);

    return () => unsubscribe();
  }, [user, filter]);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Initialize Data Component (for testing) */}
      <InitializeData />

      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Food Listings Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Real-time view of available surplus food on campus
          </p>
        </div>
        
        {/* Filter Buttons */}
        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg font-medium transition duration-200 ${
              filter === 'all'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('available')}
            className={`px-4 py-2 rounded-lg font-medium transition duration-200 ${
              filter === 'available'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Available
          </button>
          <button
            onClick={() => setFilter('claimed')}
            className={`px-4 py-2 rounded-lg font-medium transition duration-200 ${
              filter === 'claimed'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Claimed
          </button>
          <button
            onClick={() => setFilter('expired')}
            className={`px-4 py-2 rounded-lg font-medium transition duration-200 ${
              filter === 'expired'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Expired
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-2xl font-bold text-primary-600">
            {foodListings.filter(item => item.status === 'available').length}
          </div>
          <div className="text-gray-600">Available Now</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-2xl font-bold text-secondary-600">
            {foodListings.filter(item => item.status === 'claimed').length}
          </div>
          <div className="text-gray-600">Claimed Today</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-2xl font-bold text-gray-600">
            {foodListings.length}
          </div>
          <div className="text-gray-600">Total Listings</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-2xl font-bold text-red-600">
            {foodListings.filter(item => item.status === 'expired').length}
          </div>
          <div className="text-gray-600">Expired</div>
        </div>
      </div>

      {/* Food Listings Grid */}
      {foodListings.length === 0 ? (
        <div className="text-center py-16">
          <div className="text-6xl mb-4">🍽️</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No food listings found
          </h3>
          <p className="text-gray-600">
            {filter === 'all' 
              ? 'Be the first to list surplus food!' 
              : `No ${filter} listings at the moment.`
            }
          </p>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {foodListings.map((listing) => (
            <FoodListingCard 
              key={listing.id} 
              listing={listing}
              currentUser={user}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Dashboard;

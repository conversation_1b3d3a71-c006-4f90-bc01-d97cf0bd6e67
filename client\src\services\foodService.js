import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  query, 
  where, 
  orderBy, 
  onSnapshot, 
  serverTimestamp,
  getDocs 
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from './firebase';

// Create a new food listing
export const createFoodListing = async (foodData, imageFile, user) => {
  try {
    let imageUrl = null;

    // Upload image if provided
    if (imageFile) {
      const imageRef = ref(storage, `food-images/${Date.now()}-${imageFile.name}`);
      const snapshot = await uploadBytes(imageRef, imageFile);
      imageUrl = await getDownloadURL(snapshot.ref);
    }

    // Calculate expiry time
    const expiryTime = new Date();
    expiryTime.setHours(expiryTime.getHours() + parseInt(foodData.expiryHours));

    // Create food listing document
    const docRef = await addDoc(collection(db, 'foodListings'), {
      ...foodData,
      quantity: parseInt(foodData.quantity),
      expiryHours: parseInt(foodData.expiryHours),
      expiryTime: expiryTime,
      imageUrl: imageUrl,
      status: 'available',
      listedBy: {
        uid: user.uid,
        name: user.displayName,
        email: user.email,
      },
      claimedBy: null,
      claimedAt: null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    console.log('Food listing created with ID:', docRef.id);
    return {
      success: true,
      listingId: docRef.id
    };
  } catch (error) {
    console.error('Error creating food listing:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Update food listing
export const updateFoodListing = async (listingId, updates) => {
  try {
    const listingRef = doc(db, 'foodListings', listingId);
    await updateDoc(listingRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });

    return { success: true };
  } catch (error) {
    console.error('Error updating food listing:', error);
    return { success: false, error: error.message };
  }
};

// Claim food listing
export const claimFoodListing = async (listingId, user) => {
  try {
    const listingRef = doc(db, 'foodListings', listingId);
    await updateDoc(listingRef, {
      status: 'claimed',
      claimedBy: {
        uid: user.uid,
        name: user.displayName,
        email: user.email,
      },
      claimedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    return { success: true };
  } catch (error) {
    console.error('Error claiming food listing:', error);
    return { success: false, error: error.message };
  }
};

// Delete food listing
export const deleteFoodListing = async (listingId, imageUrl) => {
  try {
    // Delete image from storage if exists
    if (imageUrl) {
      const imageRef = ref(storage, imageUrl);
      await deleteObject(imageRef);
    }

    // Delete document from Firestore
    await deleteDoc(doc(db, 'foodListings', listingId));

    return { success: true };
  } catch (error) {
    console.error('Error deleting food listing:', error);
    return { success: false, error: error.message };
  }
};

// Get food listings with real-time updates
export const subscribeFoodListings = (callback, filters = {}) => {
  try {
    let q = collection(db, 'foodListings');

    // Apply filters
    if (filters.status) {
      q = query(q, where('status', '==', filters.status));
    }
    if (filters.listedBy) {
      q = query(q, where('listedBy.uid', '==', filters.listedBy));
    }

    // Order by creation date (newest first)
    q = query(q, orderBy('createdAt', 'desc'));

    // Set up real-time listener
    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const listings = [];
      querySnapshot.forEach((doc) => {
        listings.push({
          id: doc.id,
          ...doc.data()
        });
      });
      callback(listings);
    }, (error) => {
      console.error('Error fetching food listings:', error);
      callback([]);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error setting up food listings subscription:', error);
    return () => {}; // Return empty function
  }
};

// Get user's food listings
export const getUserFoodListings = async (userId) => {
  try {
    const q = query(
      collection(db, 'foodListings'),
      where('listedBy.uid', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const listings = [];
    querySnapshot.forEach((doc) => {
      listings.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return {
      success: true,
      listings: listings
    };
  } catch (error) {
    console.error('Error getting user food listings:', error);
    return {
      success: false,
      error: error.message,
      listings: []
    };
  }
};

// Mark expired listings
export const markExpiredListings = async () => {
  try {
    const now = new Date();
    const q = query(
      collection(db, 'foodListings'),
      where('status', '==', 'available'),
      where('expiryTime', '<=', now)
    );

    const querySnapshot = await getDocs(q);
    const updatePromises = [];

    querySnapshot.forEach((doc) => {
      const listingRef = doc.ref;
      updatePromises.push(
        updateDoc(listingRef, {
          status: 'expired',
          updatedAt: serverTimestamp()
        })
      );
    });

    await Promise.all(updatePromises);
    console.log(`Marked ${updatePromises.length} listings as expired`);

    return {
      success: true,
      expiredCount: updatePromises.length
    };
  } catch (error) {
    console.error('Error marking expired listings:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Get food listing statistics
export const getFoodListingStats = async (userId = null) => {
  try {
    let q = collection(db, 'foodListings');
    
    if (userId) {
      q = query(q, where('listedBy.uid', '==', userId));
    }

    const querySnapshot = await getDocs(q);
    const stats = {
      total: 0,
      available: 0,
      claimed: 0,
      expired: 0
    };

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      stats.total++;
      stats[data.status]++;
    });

    return {
      success: true,
      stats: stats
    };
  } catch (error) {
    console.error('Error getting food listing stats:', error);
    return {
      success: false,
      error: error.message,
      stats: { total: 0, available: 0, claimed: 0, expired: 0 }
    };
  }
};

{"ast": null, "code": "// Import the functions you need from the SDKs you need\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, GoogleAuthProvider } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getMessaging, getToken, onMessage } from 'firebase/messaging';\nimport { getAnalytics } from 'firebase/analytics';\n\n// Your web app's Firebase configuration\nconst firebaseConfig = {\n  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,\n  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.REACT_APP_FIREBASE_APP_ID,\n  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase Authentication and get a reference to the service\nexport const auth = getAuth(app);\nexport const googleProvider = new GoogleAuthProvider();\n\n// Initialize Cloud Firestore and get a reference to the service\nexport const db = getFirestore(app);\n\n// Initialize Cloud Storage and get a reference to the service\nexport const storage = getStorage(app);\n\n// Initialize Firebase Cloud Messaging and get a reference to the service\nexport const messaging = getMessaging(app);\n\n// Initialize Analytics\nexport const analytics = getAnalytics(app);\n\n// Request permission for notifications and get FCM token\nexport const requestNotificationPermission = async () => {\n  try {\n    const permission = await Notification.requestPermission();\n    if (permission === 'granted') {\n      const token = await getToken(messaging, {\n        vapidKey: process.env.REACT_APP_FIREBASE_VAPID_KEY\n      });\n      console.log('FCM Token:', token);\n      return token;\n    } else {\n      console.log('Notification permission denied');\n      return null;\n    }\n  } catch (error) {\n    console.error('Error getting notification permission:', error);\n    return null;\n  }\n};\n\n// Listen for foreground messages\nexport const onMessageListener = () => new Promise(resolve => {\n  onMessage(messaging, payload => {\n    console.log('Received foreground message:', payload);\n    resolve(payload);\n  });\n});\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "GoogleAuthProvider", "getFirestore", "getStorage", "getMessaging", "getToken", "onMessage", "getAnalytics", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "REACT_APP_FIREBASE_API_KEY", "authDomain", "REACT_APP_FIREBASE_AUTH_DOMAIN", "projectId", "REACT_APP_FIREBASE_PROJECT_ID", "storageBucket", "REACT_APP_FIREBASE_STORAGE_BUCKET", "messagingSenderId", "REACT_APP_FIREBASE_MESSAGING_SENDER_ID", "appId", "REACT_APP_FIREBASE_APP_ID", "measurementId", "REACT_APP_FIREBASE_MEASUREMENT_ID", "app", "auth", "googleProvider", "db", "storage", "messaging", "analytics", "requestNotificationPermission", "permission", "Notification", "requestPermission", "token", "vapid<PERSON>ey", "REACT_APP_FIREBASE_VAPID_KEY", "console", "log", "error", "onMessageListener", "Promise", "resolve", "payload"], "sources": ["C:/Users/<USER>/Desktop/ZeroWaste/client/src/services/firebase.js"], "sourcesContent": ["// Import the functions you need from the SDKs you need\nimport { initializeApp } from 'firebase/app';\nimport { getAuth, GoogleAuthProvider } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nimport { getMessaging, getToken, onMessage } from 'firebase/messaging';\nimport { getAnalytics } from 'firebase/analytics';\n\n// Your web app's Firebase configuration\nconst firebaseConfig = {\n  apiKey: process.env.REACT_APP_FIREBASE_API_KEY,\n  authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,\n  projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,\n  storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,\n  messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,\n  appId: process.env.REACT_APP_FIREBASE_APP_ID,\n  measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID\n};\n\n// Initialize Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Initialize Firebase Authentication and get a reference to the service\nexport const auth = getAuth(app);\nexport const googleProvider = new GoogleAuthProvider();\n\n// Initialize Cloud Firestore and get a reference to the service\nexport const db = getFirestore(app);\n\n// Initialize Cloud Storage and get a reference to the service\nexport const storage = getStorage(app);\n\n// Initialize Firebase Cloud Messaging and get a reference to the service\nexport const messaging = getMessaging(app);\n\n// Initialize Analytics\nexport const analytics = getAnalytics(app);\n\n// Request permission for notifications and get FCM token\nexport const requestNotificationPermission = async () => {\n  try {\n    const permission = await Notification.requestPermission();\n    if (permission === 'granted') {\n      const token = await getToken(messaging, {\n        vapidKey: process.env.REACT_APP_FIREBASE_VAPID_KEY\n      });\n      console.log('FCM Token:', token);\n      return token;\n    } else {\n      console.log('Notification permission denied');\n      return null;\n    }\n  } catch (error) {\n    console.error('Error getting notification permission:', error);\n    return null;\n  }\n};\n\n// Listen for foreground messages\nexport const onMessageListener = () =>\n  new Promise((resolve) => {\n    onMessage(messaging, (payload) => {\n      console.log('Received foreground message:', payload);\n      resolve(payload);\n    });\n  });\n\nexport default app;\n"], "mappings": "AAAA;AACA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,EAAEC,kBAAkB,QAAQ,eAAe;AAC3D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,YAAY,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,oBAAoB;AACtE,SAASC,YAAY,QAAQ,oBAAoB;;AAEjD;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B;EAC9CC,UAAU,EAAEH,OAAO,CAACC,GAAG,CAACG,8BAA8B;EACtDC,SAAS,EAAEL,OAAO,CAACC,GAAG,CAACK,6BAA6B;EACpDC,aAAa,EAAEP,OAAO,CAACC,GAAG,CAACO,iCAAiC;EAC5DC,iBAAiB,EAAET,OAAO,CAACC,GAAG,CAACS,sCAAsC;EACrEC,KAAK,EAAEX,OAAO,CAACC,GAAG,CAACW,yBAAyB;EAC5CC,aAAa,EAAEb,OAAO,CAACC,GAAG,CAACa;AAC7B,CAAC;;AAED;AACA,MAAMC,GAAG,GAAG1B,aAAa,CAACS,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMkB,IAAI,GAAG1B,OAAO,CAACyB,GAAG,CAAC;AAChC,OAAO,MAAME,cAAc,GAAG,IAAI1B,kBAAkB,CAAC,CAAC;;AAEtD;AACA,OAAO,MAAM2B,EAAE,GAAG1B,YAAY,CAACuB,GAAG,CAAC;;AAEnC;AACA,OAAO,MAAMI,OAAO,GAAG1B,UAAU,CAACsB,GAAG,CAAC;;AAEtC;AACA,OAAO,MAAMK,SAAS,GAAG1B,YAAY,CAACqB,GAAG,CAAC;;AAE1C;AACA,OAAO,MAAMM,SAAS,GAAGxB,YAAY,CAACkB,GAAG,CAAC;;AAE1C;AACA,OAAO,MAAMO,6BAA6B,GAAG,MAAAA,CAAA,KAAY;EACvD,IAAI;IACF,MAAMC,UAAU,GAAG,MAAMC,YAAY,CAACC,iBAAiB,CAAC,CAAC;IACzD,IAAIF,UAAU,KAAK,SAAS,EAAE;MAC5B,MAAMG,KAAK,GAAG,MAAM/B,QAAQ,CAACyB,SAAS,EAAE;QACtCO,QAAQ,EAAE3B,OAAO,CAACC,GAAG,CAAC2B;MACxB,CAAC,CAAC;MACFC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEJ,KAAK,CAAC;MAChC,OAAOA,KAAK;IACd,CAAC,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAC9D,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAC/B,IAAIC,OAAO,CAAEC,OAAO,IAAK;EACvBtC,SAAS,CAACwB,SAAS,EAAGe,OAAO,IAAK;IAChCN,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,OAAO,CAAC;IACpDD,OAAO,CAACC,OAAO,CAAC;EAClB,CAAC,CAAC;AACJ,CAAC,CAAC;AAEJ,eAAepB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
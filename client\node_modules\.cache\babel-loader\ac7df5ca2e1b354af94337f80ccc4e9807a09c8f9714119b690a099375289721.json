{"ast": null, "code": "import { collection, addDoc, updateDoc, deleteDoc, doc, query, where, orderBy, onSnapshot, serverTimestamp, getDocs } from 'firebase/firestore';\nimport { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';\nimport { db, storage } from './firebase';\n\n// Create a new food listing\nexport const createFoodListing = async (foodData, imageFile, user) => {\n  try {\n    let imageUrl = null;\n\n    // Upload image if provided\n    if (imageFile) {\n      const imageRef = ref(storage, `food-images/${Date.now()}-${imageFile.name}`);\n      const snapshot = await uploadBytes(imageRef, imageFile);\n      imageUrl = await getDownloadURL(snapshot.ref);\n    }\n\n    // Calculate expiry time\n    const expiryTime = new Date();\n    expiryTime.setHours(expiryTime.getHours() + parseInt(foodData.expiryHours));\n\n    // Create food listing document\n    const docRef = await addDoc(collection(db, 'foodListings'), {\n      ...foodData,\n      quantity: parseInt(foodData.quantity),\n      expiryHours: parseInt(foodData.expiryHours),\n      expiryTime: expiryTime,\n      imageUrl: imageUrl,\n      status: 'available',\n      listedBy: {\n        uid: user.uid,\n        name: user.displayName,\n        email: user.email\n      },\n      claimedBy: null,\n      claimedAt: null,\n      createdAt: serverTimestamp(),\n      updatedAt: serverTimestamp()\n    });\n    console.log('Food listing created with ID:', docRef.id);\n    return {\n      success: true,\n      listingId: docRef.id\n    };\n  } catch (error) {\n    console.error('Error creating food listing:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Update food listing\nexport const updateFoodListing = async (listingId, updates) => {\n  try {\n    const listingRef = doc(db, 'foodListings', listingId);\n    await updateDoc(listingRef, {\n      ...updates,\n      updatedAt: serverTimestamp()\n    });\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error updating food listing:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Claim food listing\nexport const claimFoodListing = async (listingId, user) => {\n  try {\n    const listingRef = doc(db, 'foodListings', listingId);\n    await updateDoc(listingRef, {\n      status: 'claimed',\n      claimedBy: {\n        uid: user.uid,\n        name: user.displayName,\n        email: user.email\n      },\n      claimedAt: serverTimestamp(),\n      updatedAt: serverTimestamp()\n    });\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error claiming food listing:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Delete food listing\nexport const deleteFoodListing = async (listingId, imageUrl) => {\n  try {\n    // Delete image from storage if exists\n    if (imageUrl) {\n      const imageRef = ref(storage, imageUrl);\n      await deleteObject(imageRef);\n    }\n\n    // Delete document from Firestore\n    await deleteDoc(doc(db, 'foodListings', listingId));\n    return {\n      success: true\n    };\n  } catch (error) {\n    console.error('Error deleting food listing:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Get food listings with real-time updates\nexport const subscribeFoodListings = (callback, filters = {}) => {\n  try {\n    let q = collection(db, 'foodListings');\n\n    // Apply filters\n    if (filters.status) {\n      q = query(q, where('status', '==', filters.status));\n    }\n    if (filters.listedBy) {\n      q = query(q, where('listedBy.uid', '==', filters.listedBy));\n    }\n\n    // Order by creation date (newest first)\n    q = query(q, orderBy('createdAt', 'desc'));\n\n    // Set up real-time listener\n    const unsubscribe = onSnapshot(q, querySnapshot => {\n      const listings = [];\n      querySnapshot.forEach(doc => {\n        listings.push({\n          id: doc.id,\n          ...doc.data()\n        });\n      });\n      callback(listings);\n    }, error => {\n      console.error('Error fetching food listings:', error);\n      callback([]);\n    });\n    return unsubscribe;\n  } catch (error) {\n    console.error('Error setting up food listings subscription:', error);\n    return () => {}; // Return empty function\n  }\n};\n\n// Get user's food listings\nexport const getUserFoodListings = async userId => {\n  try {\n    const q = query(collection(db, 'foodListings'), where('listedBy.uid', '==', userId), orderBy('createdAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    const listings = [];\n    querySnapshot.forEach(doc => {\n      listings.push({\n        id: doc.id,\n        ...doc.data()\n      });\n    });\n    return {\n      success: true,\n      listings: listings\n    };\n  } catch (error) {\n    console.error('Error getting user food listings:', error);\n    return {\n      success: false,\n      error: error.message,\n      listings: []\n    };\n  }\n};\n\n// Mark expired listings\nexport const markExpiredListings = async () => {\n  try {\n    const now = new Date();\n    const q = query(collection(db, 'foodListings'), where('status', '==', 'available'), where('expiryTime', '<=', now));\n    const querySnapshot = await getDocs(q);\n    const updatePromises = [];\n    querySnapshot.forEach(doc => {\n      const listingRef = doc.ref;\n      updatePromises.push(updateDoc(listingRef, {\n        status: 'expired',\n        updatedAt: serverTimestamp()\n      }));\n    });\n    await Promise.all(updatePromises);\n    console.log(`Marked ${updatePromises.length} listings as expired`);\n    return {\n      success: true,\n      expiredCount: updatePromises.length\n    };\n  } catch (error) {\n    console.error('Error marking expired listings:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Get food listing statistics\nexport const getFoodListingStats = async (userId = null) => {\n  try {\n    let q = collection(db, 'foodListings');\n    if (userId) {\n      q = query(q, where('listedBy.uid', '==', userId));\n    }\n    const querySnapshot = await getDocs(q);\n    const stats = {\n      total: 0,\n      available: 0,\n      claimed: 0,\n      expired: 0\n    };\n    querySnapshot.forEach(doc => {\n      const data = doc.data();\n      stats.total++;\n      stats[data.status]++;\n    });\n    return {\n      success: true,\n      stats: stats\n    };\n  } catch (error) {\n    console.error('Error getting food listing stats:', error);\n    return {\n      success: false,\n      error: error.message,\n      stats: {\n        total: 0,\n        available: 0,\n        claimed: 0,\n        expired: 0\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["collection", "addDoc", "updateDoc", "deleteDoc", "doc", "query", "where", "orderBy", "onSnapshot", "serverTimestamp", "getDocs", "ref", "uploadBytes", "getDownloadURL", "deleteObject", "db", "storage", "createFoodListing", "foodData", "imageFile", "user", "imageUrl", "imageRef", "Date", "now", "name", "snapshot", "expiryTime", "setHours", "getHours", "parseInt", "expiryHours", "doc<PERSON>ef", "quantity", "status", "listedBy", "uid", "displayName", "email", "claimed<PERSON>y", "claimedAt", "createdAt", "updatedAt", "console", "log", "id", "success", "listingId", "error", "message", "updateFoodListing", "updates", "listingRef", "claimFoodListing", "deleteFoodListing", "subscribeFoodListings", "callback", "filters", "q", "unsubscribe", "querySnapshot", "listings", "for<PERSON>ach", "push", "data", "getUserFoodListings", "userId", "markEx<PERSON>List<PERSON>", "updatePromises", "Promise", "all", "length", "expiredCount", "getFoodListingStats", "stats", "total", "available", "claimed", "expired"], "sources": ["C:/Users/<USER>/Desktop/ZeroWaste/client/src/services/foodService.js"], "sourcesContent": ["import { \n  collection, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  doc, \n  query, \n  where, \n  orderBy, \n  onSnapshot, \n  serverTimestamp,\n  getDocs \n} from 'firebase/firestore';\nimport { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';\nimport { db, storage } from './firebase';\n\n// Create a new food listing\nexport const createFoodListing = async (foodData, imageFile, user) => {\n  try {\n    let imageUrl = null;\n\n    // Upload image if provided\n    if (imageFile) {\n      const imageRef = ref(storage, `food-images/${Date.now()}-${imageFile.name}`);\n      const snapshot = await uploadBytes(imageRef, imageFile);\n      imageUrl = await getDownloadURL(snapshot.ref);\n    }\n\n    // Calculate expiry time\n    const expiryTime = new Date();\n    expiryTime.setHours(expiryTime.getHours() + parseInt(foodData.expiryHours));\n\n    // Create food listing document\n    const docRef = await addDoc(collection(db, 'foodListings'), {\n      ...foodData,\n      quantity: parseInt(foodData.quantity),\n      expiryHours: parseInt(foodData.expiryHours),\n      expiryTime: expiryTime,\n      imageUrl: imageUrl,\n      status: 'available',\n      listedBy: {\n        uid: user.uid,\n        name: user.displayName,\n        email: user.email,\n      },\n      claimedBy: null,\n      claimedAt: null,\n      createdAt: serverTimestamp(),\n      updatedAt: serverTimestamp(),\n    });\n\n    console.log('Food listing created with ID:', docRef.id);\n    return {\n      success: true,\n      listingId: docRef.id\n    };\n  } catch (error) {\n    console.error('Error creating food listing:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Update food listing\nexport const updateFoodListing = async (listingId, updates) => {\n  try {\n    const listingRef = doc(db, 'foodListings', listingId);\n    await updateDoc(listingRef, {\n      ...updates,\n      updatedAt: serverTimestamp()\n    });\n\n    return { success: true };\n  } catch (error) {\n    console.error('Error updating food listing:', error);\n    return { success: false, error: error.message };\n  }\n};\n\n// Claim food listing\nexport const claimFoodListing = async (listingId, user) => {\n  try {\n    const listingRef = doc(db, 'foodListings', listingId);\n    await updateDoc(listingRef, {\n      status: 'claimed',\n      claimedBy: {\n        uid: user.uid,\n        name: user.displayName,\n        email: user.email,\n      },\n      claimedAt: serverTimestamp(),\n      updatedAt: serverTimestamp(),\n    });\n\n    return { success: true };\n  } catch (error) {\n    console.error('Error claiming food listing:', error);\n    return { success: false, error: error.message };\n  }\n};\n\n// Delete food listing\nexport const deleteFoodListing = async (listingId, imageUrl) => {\n  try {\n    // Delete image from storage if exists\n    if (imageUrl) {\n      const imageRef = ref(storage, imageUrl);\n      await deleteObject(imageRef);\n    }\n\n    // Delete document from Firestore\n    await deleteDoc(doc(db, 'foodListings', listingId));\n\n    return { success: true };\n  } catch (error) {\n    console.error('Error deleting food listing:', error);\n    return { success: false, error: error.message };\n  }\n};\n\n// Get food listings with real-time updates\nexport const subscribeFoodListings = (callback, filters = {}) => {\n  try {\n    let q = collection(db, 'foodListings');\n\n    // Apply filters\n    if (filters.status) {\n      q = query(q, where('status', '==', filters.status));\n    }\n    if (filters.listedBy) {\n      q = query(q, where('listedBy.uid', '==', filters.listedBy));\n    }\n\n    // Order by creation date (newest first)\n    q = query(q, orderBy('createdAt', 'desc'));\n\n    // Set up real-time listener\n    const unsubscribe = onSnapshot(q, (querySnapshot) => {\n      const listings = [];\n      querySnapshot.forEach((doc) => {\n        listings.push({\n          id: doc.id,\n          ...doc.data()\n        });\n      });\n      callback(listings);\n    }, (error) => {\n      console.error('Error fetching food listings:', error);\n      callback([]);\n    });\n\n    return unsubscribe;\n  } catch (error) {\n    console.error('Error setting up food listings subscription:', error);\n    return () => {}; // Return empty function\n  }\n};\n\n// Get user's food listings\nexport const getUserFoodListings = async (userId) => {\n  try {\n    const q = query(\n      collection(db, 'foodListings'),\n      where('listedBy.uid', '==', userId),\n      orderBy('createdAt', 'desc')\n    );\n\n    const querySnapshot = await getDocs(q);\n    const listings = [];\n    querySnapshot.forEach((doc) => {\n      listings.push({\n        id: doc.id,\n        ...doc.data()\n      });\n    });\n\n    return {\n      success: true,\n      listings: listings\n    };\n  } catch (error) {\n    console.error('Error getting user food listings:', error);\n    return {\n      success: false,\n      error: error.message,\n      listings: []\n    };\n  }\n};\n\n// Mark expired listings\nexport const markExpiredListings = async () => {\n  try {\n    const now = new Date();\n    const q = query(\n      collection(db, 'foodListings'),\n      where('status', '==', 'available'),\n      where('expiryTime', '<=', now)\n    );\n\n    const querySnapshot = await getDocs(q);\n    const updatePromises = [];\n\n    querySnapshot.forEach((doc) => {\n      const listingRef = doc.ref;\n      updatePromises.push(\n        updateDoc(listingRef, {\n          status: 'expired',\n          updatedAt: serverTimestamp()\n        })\n      );\n    });\n\n    await Promise.all(updatePromises);\n    console.log(`Marked ${updatePromises.length} listings as expired`);\n\n    return {\n      success: true,\n      expiredCount: updatePromises.length\n    };\n  } catch (error) {\n    console.error('Error marking expired listings:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n};\n\n// Get food listing statistics\nexport const getFoodListingStats = async (userId = null) => {\n  try {\n    let q = collection(db, 'foodListings');\n    \n    if (userId) {\n      q = query(q, where('listedBy.uid', '==', userId));\n    }\n\n    const querySnapshot = await getDocs(q);\n    const stats = {\n      total: 0,\n      available: 0,\n      claimed: 0,\n      expired: 0\n    };\n\n    querySnapshot.forEach((doc) => {\n      const data = doc.data();\n      stats.total++;\n      stats[data.status]++;\n    });\n\n    return {\n      success: true,\n      stats: stats\n    };\n  } catch (error) {\n    console.error('Error getting food listing stats:', error);\n    return {\n      success: false,\n      error: error.message,\n      stats: { total: 0, available: 0, claimed: 0, expired: 0 }\n    };\n  }\n};\n"], "mappings": "AAAA,SACEA,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,eAAe,EACfC,OAAO,QACF,oBAAoB;AAC3B,SAASC,GAAG,EAAEC,WAAW,EAAEC,cAAc,EAAEC,YAAY,QAAQ,kBAAkB;AACjF,SAASC,EAAE,EAAEC,OAAO,QAAQ,YAAY;;AAExC;AACA,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,KAAK;EACpE,IAAI;IACF,IAAIC,QAAQ,GAAG,IAAI;;IAEnB;IACA,IAAIF,SAAS,EAAE;MACb,MAAMG,QAAQ,GAAGX,GAAG,CAACK,OAAO,EAAE,eAAeO,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIL,SAAS,CAACM,IAAI,EAAE,CAAC;MAC5E,MAAMC,QAAQ,GAAG,MAAMd,WAAW,CAACU,QAAQ,EAAEH,SAAS,CAAC;MACvDE,QAAQ,GAAG,MAAMR,cAAc,CAACa,QAAQ,CAACf,GAAG,CAAC;IAC/C;;IAEA;IACA,MAAMgB,UAAU,GAAG,IAAIJ,IAAI,CAAC,CAAC;IAC7BI,UAAU,CAACC,QAAQ,CAACD,UAAU,CAACE,QAAQ,CAAC,CAAC,GAAGC,QAAQ,CAACZ,QAAQ,CAACa,WAAW,CAAC,CAAC;;IAE3E;IACA,MAAMC,MAAM,GAAG,MAAM/B,MAAM,CAACD,UAAU,CAACe,EAAE,EAAE,cAAc,CAAC,EAAE;MAC1D,GAAGG,QAAQ;MACXe,QAAQ,EAAEH,QAAQ,CAACZ,QAAQ,CAACe,QAAQ,CAAC;MACrCF,WAAW,EAAED,QAAQ,CAACZ,QAAQ,CAACa,WAAW,CAAC;MAC3CJ,UAAU,EAAEA,UAAU;MACtBN,QAAQ,EAAEA,QAAQ;MAClBa,MAAM,EAAE,WAAW;MACnBC,QAAQ,EAAE;QACRC,GAAG,EAAEhB,IAAI,CAACgB,GAAG;QACbX,IAAI,EAAEL,IAAI,CAACiB,WAAW;QACtBC,KAAK,EAAElB,IAAI,CAACkB;MACd,CAAC;MACDC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAEhC,eAAe,CAAC,CAAC;MAC5BiC,SAAS,EAAEjC,eAAe,CAAC;IAC7B,CAAC,CAAC;IAEFkC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEZ,MAAM,CAACa,EAAE,CAAC;IACvD,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAEf,MAAM,CAACa;IACpB,CAAC;EACH,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO;MACLF,OAAO,EAAE,KAAK;MACdE,KAAK,EAAEA,KAAK,CAACC;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAOH,SAAS,EAAEI,OAAO,KAAK;EAC7D,IAAI;IACF,MAAMC,UAAU,GAAGhD,GAAG,CAACW,EAAE,EAAE,cAAc,EAAEgC,SAAS,CAAC;IACrD,MAAM7C,SAAS,CAACkD,UAAU,EAAE;MAC1B,GAAGD,OAAO;MACVT,SAAS,EAAEjC,eAAe,CAAC;IAC7B,CAAC,CAAC;IAEF,OAAO;MAAEqC,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO;MAAEF,OAAO,EAAE,KAAK;MAAEE,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,gBAAgB,GAAG,MAAAA,CAAON,SAAS,EAAE3B,IAAI,KAAK;EACzD,IAAI;IACF,MAAMgC,UAAU,GAAGhD,GAAG,CAACW,EAAE,EAAE,cAAc,EAAEgC,SAAS,CAAC;IACrD,MAAM7C,SAAS,CAACkD,UAAU,EAAE;MAC1BlB,MAAM,EAAE,SAAS;MACjBK,SAAS,EAAE;QACTH,GAAG,EAAEhB,IAAI,CAACgB,GAAG;QACbX,IAAI,EAAEL,IAAI,CAACiB,WAAW;QACtBC,KAAK,EAAElB,IAAI,CAACkB;MACd,CAAC;MACDE,SAAS,EAAE/B,eAAe,CAAC,CAAC;MAC5BiC,SAAS,EAAEjC,eAAe,CAAC;IAC7B,CAAC,CAAC;IAEF,OAAO;MAAEqC,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO;MAAEF,OAAO,EAAE,KAAK;MAAEE,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,iBAAiB,GAAG,MAAAA,CAAOP,SAAS,EAAE1B,QAAQ,KAAK;EAC9D,IAAI;IACF;IACA,IAAIA,QAAQ,EAAE;MACZ,MAAMC,QAAQ,GAAGX,GAAG,CAACK,OAAO,EAAEK,QAAQ,CAAC;MACvC,MAAMP,YAAY,CAACQ,QAAQ,CAAC;IAC9B;;IAEA;IACA,MAAMnB,SAAS,CAACC,GAAG,CAACW,EAAE,EAAE,cAAc,EAAEgC,SAAS,CAAC,CAAC;IAEnD,OAAO;MAAED,OAAO,EAAE;IAAK,CAAC;EAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO;MAAEF,OAAO,EAAE,KAAK;MAAEE,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,qBAAqB,GAAGA,CAACC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EAC/D,IAAI;IACF,IAAIC,CAAC,GAAG1D,UAAU,CAACe,EAAE,EAAE,cAAc,CAAC;;IAEtC;IACA,IAAI0C,OAAO,CAACvB,MAAM,EAAE;MAClBwB,CAAC,GAAGrD,KAAK,CAACqD,CAAC,EAAEpD,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAEmD,OAAO,CAACvB,MAAM,CAAC,CAAC;IACrD;IACA,IAAIuB,OAAO,CAACtB,QAAQ,EAAE;MACpBuB,CAAC,GAAGrD,KAAK,CAACqD,CAAC,EAAEpD,KAAK,CAAC,cAAc,EAAE,IAAI,EAAEmD,OAAO,CAACtB,QAAQ,CAAC,CAAC;IAC7D;;IAEA;IACAuB,CAAC,GAAGrD,KAAK,CAACqD,CAAC,EAAEnD,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;;IAE1C;IACA,MAAMoD,WAAW,GAAGnD,UAAU,CAACkD,CAAC,EAAGE,aAAa,IAAK;MACnD,MAAMC,QAAQ,GAAG,EAAE;MACnBD,aAAa,CAACE,OAAO,CAAE1D,GAAG,IAAK;QAC7ByD,QAAQ,CAACE,IAAI,CAAC;UACZlB,EAAE,EAAEzC,GAAG,CAACyC,EAAE;UACV,GAAGzC,GAAG,CAAC4D,IAAI,CAAC;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MACFR,QAAQ,CAACK,QAAQ,CAAC;IACpB,CAAC,EAAGb,KAAK,IAAK;MACZL,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDQ,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC;IAEF,OAAOG,WAAW;EACpB,CAAC,CAAC,OAAOX,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACpE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;EACnB;AACF,CAAC;;AAED;AACA,OAAO,MAAMiB,mBAAmB,GAAG,MAAOC,MAAM,IAAK;EACnD,IAAI;IACF,MAAMR,CAAC,GAAGrD,KAAK,CACbL,UAAU,CAACe,EAAE,EAAE,cAAc,CAAC,EAC9BT,KAAK,CAAC,cAAc,EAAE,IAAI,EAAE4D,MAAM,CAAC,EACnC3D,OAAO,CAAC,WAAW,EAAE,MAAM,CAC7B,CAAC;IAED,MAAMqD,aAAa,GAAG,MAAMlD,OAAO,CAACgD,CAAC,CAAC;IACtC,MAAMG,QAAQ,GAAG,EAAE;IACnBD,aAAa,CAACE,OAAO,CAAE1D,GAAG,IAAK;MAC7ByD,QAAQ,CAACE,IAAI,CAAC;QACZlB,EAAE,EAAEzC,GAAG,CAACyC,EAAE;QACV,GAAGzC,GAAG,CAAC4D,IAAI,CAAC;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACLlB,OAAO,EAAE,IAAI;MACbe,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,OAAOb,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,OAAO;MACLF,OAAO,EAAE,KAAK;MACdE,KAAK,EAAEA,KAAK,CAACC,OAAO;MACpBY,QAAQ,EAAE;IACZ,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EAC7C,IAAI;IACF,MAAM3C,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMmC,CAAC,GAAGrD,KAAK,CACbL,UAAU,CAACe,EAAE,EAAE,cAAc,CAAC,EAC9BT,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,WAAW,CAAC,EAClCA,KAAK,CAAC,YAAY,EAAE,IAAI,EAAEkB,GAAG,CAC/B,CAAC;IAED,MAAMoC,aAAa,GAAG,MAAMlD,OAAO,CAACgD,CAAC,CAAC;IACtC,MAAMU,cAAc,GAAG,EAAE;IAEzBR,aAAa,CAACE,OAAO,CAAE1D,GAAG,IAAK;MAC7B,MAAMgD,UAAU,GAAGhD,GAAG,CAACO,GAAG;MAC1ByD,cAAc,CAACL,IAAI,CACjB7D,SAAS,CAACkD,UAAU,EAAE;QACpBlB,MAAM,EAAE,SAAS;QACjBQ,SAAS,EAAEjC,eAAe,CAAC;MAC7B,CAAC,CACH,CAAC;IACH,CAAC,CAAC;IAEF,MAAM4D,OAAO,CAACC,GAAG,CAACF,cAAc,CAAC;IACjCzB,OAAO,CAACC,GAAG,CAAC,UAAUwB,cAAc,CAACG,MAAM,sBAAsB,CAAC;IAElE,OAAO;MACLzB,OAAO,EAAE,IAAI;MACb0B,YAAY,EAAEJ,cAAc,CAACG;IAC/B,CAAC;EACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,OAAO;MACLF,OAAO,EAAE,KAAK;MACdE,KAAK,EAAEA,KAAK,CAACC;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMwB,mBAAmB,GAAG,MAAAA,CAAOP,MAAM,GAAG,IAAI,KAAK;EAC1D,IAAI;IACF,IAAIR,CAAC,GAAG1D,UAAU,CAACe,EAAE,EAAE,cAAc,CAAC;IAEtC,IAAImD,MAAM,EAAE;MACVR,CAAC,GAAGrD,KAAK,CAACqD,CAAC,EAAEpD,KAAK,CAAC,cAAc,EAAE,IAAI,EAAE4D,MAAM,CAAC,CAAC;IACnD;IAEA,MAAMN,aAAa,GAAG,MAAMlD,OAAO,CAACgD,CAAC,CAAC;IACtC,MAAMgB,KAAK,GAAG;MACZC,KAAK,EAAE,CAAC;MACRC,SAAS,EAAE,CAAC;MACZC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;IAEDlB,aAAa,CAACE,OAAO,CAAE1D,GAAG,IAAK;MAC7B,MAAM4D,IAAI,GAAG5D,GAAG,CAAC4D,IAAI,CAAC,CAAC;MACvBU,KAAK,CAACC,KAAK,EAAE;MACbD,KAAK,CAACV,IAAI,CAAC9B,MAAM,CAAC,EAAE;IACtB,CAAC,CAAC;IAEF,OAAO;MACLY,OAAO,EAAE,IAAI;MACb4B,KAAK,EAAEA;IACT,CAAC;EACH,CAAC,CAAC,OAAO1B,KAAK,EAAE;IACdL,OAAO,CAACK,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,OAAO;MACLF,OAAO,EAAE,KAAK;MACdE,KAAK,EAAEA,KAAK,CAACC,OAAO;MACpByB,KAAK,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE;IAC1D,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
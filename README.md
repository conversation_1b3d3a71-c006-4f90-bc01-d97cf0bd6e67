# 🍲 ZeroWaste Campus – Smart Surplus Food Redistribution Platform

## 📌 Overview
**ZeroWaste Campus** is a real-time food redistribution platform that connects campus canteens, hostels, and event organizers with students, staff, and NGOs to **reduce food waste**.  
It enables:
- Surplus food listing
- Instant notifications
- Pickup coordination
- Expiry tracking
- Impact analytics

This project is designed to **qualify and excel in hackathon rounds** with a **functional MVP** that is production-ready in the future.

---

## 🚀 Features
1. **Surplus Food Listing**
   - Canteens/hostels log extra food with:
     - Name, quantity, freshness status, expiry time, location
     - Optional image upload
2. **Real-Time Notifications**
   - Sends push alerts to registered users upon new listings
3. **Pickup Claim System**
   - Allows users to claim food for pickup
4. **Food Safety & Expiry Tracking**
   - Tags like "Safe to eat for X hours"
   - Auto-remove after expiry
5. **Impact Dashboard**
   - Tracks:
     - Total food saved
     - People served (estimated)
     - CO₂/water footprint avoided

---

## 🏗 Architecture
Frontend (React.js + Tailwind CSS) → Firebase Firestore (DB)
→ Firebase Auth (Authentication)
→ Firebase Cloud Messaging (Push Notifications)
→ Firebase Hosting (Deployment)

---

## Project Structure
zerowaste-campus/
├── client/ # Frontend (React.js)
│ ├── src/
│ │ ├── components/ # UI components
│ │ ├── pages/ # Page views
│ │ ├── services/ # API/Firebase calls
│ │ ├── utils/ # Helper functions
│ │ └── App.js
├── functions/ # Cloud functions (for expiry cleanup, etc.)
│ └── index.js
├── README.md
└── package.json

## ⚙️ Tech Stack
- **Frontend:** React.js + Tailwind CSS
- **Backend:** Firebase Firestore
- **Auth:** Firebase Authentication (Google Sign-In)
- **Notifications:** Firebase Cloud Messaging
- **Deployment:** Firebase Hosting
- **Charts:** Chart.js / Recharts

---

## 📝 License
This project is licensed under the MIT License.

---

## 📢 Contact
For any queries, feel free to reach out:
- Email: [<EMAIL>](mailto:<EMAIL>)

{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "chart.js": "^4.5.0", "firebase": "^12.1.0", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-firebase-hooks": "^5.1.1", "react-router-dom": "^7.8.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}
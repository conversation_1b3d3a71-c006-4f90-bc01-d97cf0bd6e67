{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ZeroWaste\\\\client\\\\src\\\\components\\\\InitializeData.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { initializeFirestore } from '../services/firestoreSetup';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InitializeData = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [initialized, setInitialized] = useState(false);\n  const handleInitialize = async () => {\n    setLoading(true);\n    try {\n      const success = await initializeFirestore();\n      if (success) {\n        setInitialized(true);\n        alert('Sample data initialized successfully!');\n      } else {\n        alert('Failed to initialize sample data. Check console for errors.');\n      }\n    } catch (error) {\n      console.error('Error initializing data:', error);\n      alert('Error initializing data. Check console for errors.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (initialized) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-green-600 mr-3\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-green-800 font-semibold\",\n            children: \"Sample Data Initialized\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-green-700 text-sm\",\n            children: \"Sample food listings and user data have been added to the database.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-blue-800 font-semibold\",\n          children: \"Initialize Sample Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-700 text-sm\",\n          children: \"Add sample food listings and user data to test the application.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleInitialize,\n        disabled: loading,\n        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this), \"Initializing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this) : 'Initialize Data'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(InitializeData, \"kk0rRGycIfm6Hfm8bg177v1y/f0=\");\n_c = InitializeData;\nexport default InitializeData;\nvar _c;\n$RefreshReg$(_c, \"InitializeData\");", "map": {"version": 3, "names": ["React", "useState", "initializeFirestore", "jsxDEV", "_jsxDEV", "InitializeData", "_s", "loading", "setLoading", "initialized", "setInitialized", "handleInitialize", "success", "alert", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/ZeroWaste/client/src/components/InitializeData.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { initializeFirestore } from '../services/firestoreSetup';\n\nconst InitializeData = () => {\n  const [loading, setLoading] = useState(false);\n  const [initialized, setInitialized] = useState(false);\n\n  const handleInitialize = async () => {\n    setLoading(true);\n    try {\n      const success = await initializeFirestore();\n      if (success) {\n        setInitialized(true);\n        alert('Sample data initialized successfully!');\n      } else {\n        alert('Failed to initialize sample data. Check console for errors.');\n      }\n    } catch (error) {\n      console.error('Error initializing data:', error);\n      alert('Error initializing data. Check console for errors.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (initialized) {\n    return (\n      <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\">\n        <div className=\"flex items-center\">\n          <div className=\"text-green-600 mr-3\">✅</div>\n          <div>\n            <h3 className=\"text-green-800 font-semibold\">Sample Data Initialized</h3>\n            <p className=\"text-green-700 text-sm\">\n              Sample food listings and user data have been added to the database.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h3 className=\"text-blue-800 font-semibold\">Initialize Sample Data</h3>\n          <p className=\"text-blue-700 text-sm\">\n            Add sample food listings and user data to test the application.\n          </p>\n        </div>\n        <button\n          onClick={handleInitialize}\n          disabled={loading}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50\"\n        >\n          {loading ? (\n            <div className=\"flex items-center\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              Initializing...\n            </div>\n          ) : (\n            'Initialize Data'\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default InitializeData;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,mBAAmB,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACQ,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMU,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMI,OAAO,GAAG,MAAMV,mBAAmB,CAAC,CAAC;MAC3C,IAAIU,OAAO,EAAE;QACXF,cAAc,CAAC,IAAI,CAAC;QACpBG,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC,MAAM;QACLA,KAAK,CAAC,6DAA6D,CAAC;MACtE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDD,KAAK,CAAC,oDAAoD,CAAC;IAC7D,CAAC,SAAS;MACRL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAIC,WAAW,EAAE;IACf,oBACEL,OAAA;MAAKY,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtEb,OAAA;QAAKY,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCb,OAAA;UAAKY,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5CjB,OAAA;UAAAa,QAAA,gBACEb,OAAA;YAAIY,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEjB,OAAA;YAAGY,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEjB,OAAA;IAAKY,SAAS,EAAC,uDAAuD;IAAAC,QAAA,eACpEb,OAAA;MAAKY,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDb,OAAA;QAAAa,QAAA,gBACEb,OAAA;UAAIY,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEjB,OAAA;UAAGY,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNjB,OAAA;QACEkB,OAAO,EAAEX,gBAAiB;QAC1BY,QAAQ,EAAEhB,OAAQ;QAClBS,SAAS,EAAC,2GAA2G;QAAAC,QAAA,EAEpHV,OAAO,gBACNH,OAAA;UAAKY,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCb,OAAA;YAAKY,SAAS,EAAC;UAAgE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,mBAExF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GAEN;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CAhEID,cAAc;AAAAmB,EAAA,GAAdnB,cAAc;AAkEpB,eAAeA,cAAc;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
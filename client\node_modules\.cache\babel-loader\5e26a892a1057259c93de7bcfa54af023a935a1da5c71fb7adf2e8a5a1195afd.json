{"ast": null, "code": "import { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Logger } from '@firebase/logger';\nimport { ErrorFactory, calculateBackoffMillis, FirebaseError, isIndexedDBAvailable, validateIndexedDBOpenable, isBrowserExtension, areCookiesEnabled, getModularInstance, deepEqual } from '@firebase/util';\nimport { Component } from '@firebase/component';\nimport '@firebase/installations';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Type constant for Firebase Analytics.\n */\nconst ANALYTICS_TYPE = 'analytics';\n// Key to attach FID to in gtag params.\nconst GA_FID_KEY = 'firebase_id';\nconst ORIGIN_KEY = 'origin';\nconst FETCH_TIMEOUT_MILLIS = 60 * 1000;\nconst DYNAMIC_CONFIG_URL = 'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\nconst GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst logger = new Logger('@firebase/analytics');\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERRORS = {\n  [\"already-exists\" /* AnalyticsError.ALREADY_EXISTS */]: 'A Firebase Analytics instance with the appId {$id} ' + ' already exists. ' + 'Only one Firebase Analytics instance can be created for each appId.',\n  [\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */]: 'initializeAnalytics() cannot be called again with different options than those ' + 'it was initially called with. It can be called again with the same options to ' + 'return the existing instance, or getAnalytics() can be used ' + 'to get a reference to the already-initialized instance.',\n  [\"already-initialized-settings\" /* AnalyticsError.ALREADY_INITIALIZED_SETTINGS */]: 'Firebase Analytics has already been initialized.' + 'settings() must be called before initializing any Analytics instance' + 'or it will have no effect.',\n  [\"interop-component-reg-failed\" /* AnalyticsError.INTEROP_COMPONENT_REG_FAILED */]: 'Firebase Analytics Interop Component failed to instantiate: {$reason}',\n  [\"invalid-analytics-context\" /* AnalyticsError.INVALID_ANALYTICS_CONTEXT */]: 'Firebase Analytics is not supported in this environment. ' + 'Wrap initialization of analytics in analytics.isSupported() ' + 'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */]: 'IndexedDB unavailable or restricted in this environment. ' + 'Wrap initialization of analytics in analytics.isSupported() ' + 'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [\"fetch-throttle\" /* AnalyticsError.FETCH_THROTTLE */]: 'The config fetch request timed out while in an exponential backoff state.' + ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [\"config-fetch-failed\" /* AnalyticsError.CONFIG_FETCH_FAILED */]: 'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\n  [\"no-api-key\" /* AnalyticsError.NO_API_KEY */]: 'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' + 'contain a valid API key.',\n  [\"no-app-id\" /* AnalyticsError.NO_APP_ID */]: 'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' + 'contain a valid app ID.',\n  [\"no-client-id\" /* AnalyticsError.NO_CLIENT_ID */]: 'The \"client_id\" field is empty.',\n  [\"invalid-gtag-resource\" /* AnalyticsError.INVALID_GTAG_RESOURCE */]: 'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\n};\nconst ERROR_FACTORY = new ErrorFactory('analytics', 'Analytics', ERRORS);\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Verifies and creates a TrustedScriptURL.\n */\nfunction createGtagTrustedTypesScriptURL(url) {\n  if (!url.startsWith(GTAG_URL)) {\n    const err = ERROR_FACTORY.create(\"invalid-gtag-resource\" /* AnalyticsError.INVALID_GTAG_RESOURCE */, {\n      gtagURL: url\n    });\n    logger.warn(err.message);\n    return '';\n  }\n  return url;\n}\n/**\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\n * have either resolved or rejected.\n *\n * @param promises Array of promises to wait for.\n */\nfunction promiseAllSettled(promises) {\n  return Promise.all(promises.map(promise => promise.catch(e => e)));\n}\n/**\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\n *\n * @param policyName A string containing the name of the policy\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\n * | the TrustedTypePolicy reference documentation}.\n */\nfunction createTrustedTypesPolicy(policyName, policyOptions) {\n  // Create a TrustedTypes policy that we can use for updating src\n  // properties\n  let trustedTypesPolicy;\n  if (window.trustedTypes) {\n    trustedTypesPolicy = window.trustedTypes.createPolicy(policyName, policyOptions);\n  }\n  return trustedTypesPolicy;\n}\n/**\n * Inserts gtag script tag into the page to asynchronously download gtag.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nfunction insertScriptTag(dataLayerName, measurementId) {\n  const trustedTypesPolicy = createTrustedTypesPolicy('firebase-js-sdk-policy', {\n    createScriptURL: createGtagTrustedTypesScriptURL\n  });\n  const script = document.createElement('script');\n  // We are not providing an analyticsId in the URL because it would trigger a `page_view`\n  // without fid. We will initialize ga-id using gtag (config) command together with fid.\n  const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\n  script.src = trustedTypesPolicy ? trustedTypesPolicy?.createScriptURL(gtagScriptURL) : gtagScriptURL;\n  script.async = true;\n  document.head.appendChild(script);\n}\n/**\n * Get reference to, or create, global datalayer.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nfunction getOrCreateDataLayer(dataLayerName) {\n  // Check for existing dataLayer and create if needed.\n  let dataLayer = [];\n  if (Array.isArray(window[dataLayerName])) {\n    dataLayer = window[dataLayerName];\n  } else {\n    window[dataLayerName] = dataLayer;\n  }\n  return dataLayer;\n}\n/**\n * Wrapped gtag logic when gtag is called with 'config' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param measurementId GA Measurement ID to set config for.\n * @param gtagParams Gtag config params to set.\n */\nasync function gtagOnConfig(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, measurementId, gtagParams) {\n  // If config is already fetched, we know the appId and can use it to look up what FID promise we\n  /// are waiting for, and wait only on that one.\n  const correspondingAppId = measurementIdToAppId[measurementId];\n  try {\n    if (correspondingAppId) {\n      await initializationPromisesMap[correspondingAppId];\n    } else {\n      // If config is not fetched yet, wait for all configs (we don't know which one we need) and\n      // find the appId (if any) corresponding to this measurementId. If there is one, wait on\n      // that appId's initialization promise. If there is none, promise resolves and gtag\n      // call goes through.\n      const dynamicConfigResults = await promiseAllSettled(dynamicConfigPromisesList);\n      const foundConfig = dynamicConfigResults.find(config => config.measurementId === measurementId);\n      if (foundConfig) {\n        await initializationPromisesMap[foundConfig.appId];\n      }\n    }\n  } catch (e) {\n    logger.error(e);\n  }\n  gtagCore(\"config\" /* GtagCommand.CONFIG */, measurementId, gtagParams);\n}\n/**\n * Wrapped gtag logic when gtag is called with 'event' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementId GA Measurement ID to log event to.\n * @param gtagParams Params to log with this event.\n */\nasync function gtagOnEvent(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementId, gtagParams) {\n  try {\n    let initializationPromisesToWaitFor = [];\n    // If there's a 'send_to' param, check if any ID specified matches\n    // an initializeIds() promise we are waiting for.\n    if (gtagParams && gtagParams['send_to']) {\n      let gaSendToList = gtagParams['send_to'];\n      // Make it an array if is isn't, so it can be dealt with the same way.\n      if (!Array.isArray(gaSendToList)) {\n        gaSendToList = [gaSendToList];\n      }\n      // Checking 'send_to' fields requires having all measurement ID results back from\n      // the dynamic config fetch.\n      const dynamicConfigResults = await promiseAllSettled(dynamicConfigPromisesList);\n      for (const sendToId of gaSendToList) {\n        // Any fetched dynamic measurement ID that matches this 'send_to' ID\n        const foundConfig = dynamicConfigResults.find(config => config.measurementId === sendToId);\n        const initializationPromise = foundConfig && initializationPromisesMap[foundConfig.appId];\n        if (initializationPromise) {\n          initializationPromisesToWaitFor.push(initializationPromise);\n        } else {\n          // Found an item in 'send_to' that is not associated\n          // directly with an FID, possibly a group.  Empty this array,\n          // exit the loop early, and let it get populated below.\n          initializationPromisesToWaitFor = [];\n          break;\n        }\n      }\n    }\n    // This will be unpopulated if there was no 'send_to' field , or\n    // if not all entries in the 'send_to' field could be mapped to\n    // a FID. In these cases, wait on all pending initialization promises.\n    if (initializationPromisesToWaitFor.length === 0) {\n      /* eslint-disable-next-line @typescript-eslint/no-floating-promises */\n      initializationPromisesToWaitFor = Object.values(initializationPromisesMap);\n    }\n    // Run core gtag function with args after all relevant initialization\n    // promises have been resolved.\n    await Promise.all(initializationPromisesToWaitFor);\n    // Workaround for http://b/141370449 - third argument cannot be undefined.\n    gtagCore(\"event\" /* GtagCommand.EVENT */, measurementId, gtagParams || {});\n  } catch (e) {\n    logger.error(e);\n  }\n}\n/**\n * Wraps a standard gtag function with extra code to wait for completion of\n * relevant initialization promises before sending requests.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n */\nfunction wrapGtag(gtagCore,\n/**\n * Allows wrapped gtag calls to wait on whichever initialization promises are required,\n * depending on the contents of the gtag params' `send_to` field, if any.\n */\ninitializationPromisesMap,\n/**\n * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\n * before determining what initialization promises (which include FIDs) to wait for.\n */\ndynamicConfigPromisesList,\n/**\n * Wrapped gtag config calls can narrow down which initialization promise (with FID)\n * to wait for if the measurementId is already fetched, by getting the corresponding appId,\n * which is the key for the initialization promises map.\n */\nmeasurementIdToAppId) {\n  /**\n   * Wrapper around gtag that ensures FID is sent with gtag calls.\n   * @param command Gtag command type.\n   * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\n   * @param gtagParams Params if event is EVENT/CONFIG.\n   */\n  async function gtagWrapper(command, ...args) {\n    try {\n      // If event, check that relevant initialization promises have completed.\n      if (command === \"event\" /* GtagCommand.EVENT */) {\n        const [measurementId, gtagParams] = args;\n        // If EVENT, second arg must be measurementId.\n        await gtagOnEvent(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementId, gtagParams);\n      } else if (command === \"config\" /* GtagCommand.CONFIG */) {\n        const [measurementId, gtagParams] = args;\n        // If CONFIG, second arg must be measurementId.\n        await gtagOnConfig(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, measurementId, gtagParams);\n      } else if (command === \"consent\" /* GtagCommand.CONSENT */) {\n        const [consentAction, gtagParams] = args;\n        // consentAction can be one of 'default' or 'update'.\n        gtagCore(\"consent\" /* GtagCommand.CONSENT */, consentAction, gtagParams);\n      } else if (command === \"get\" /* GtagCommand.GET */) {\n        const [measurementId, fieldName, callback] = args;\n        gtagCore(\"get\" /* GtagCommand.GET */, measurementId, fieldName, callback);\n      } else if (command === \"set\" /* GtagCommand.SET */) {\n        const [customParams] = args;\n        // If SET, second arg must be params.\n        gtagCore(\"set\" /* GtagCommand.SET */, customParams);\n      } else {\n        gtagCore(command, ...args);\n      }\n    } catch (e) {\n      logger.error(e);\n    }\n  }\n  return gtagWrapper;\n}\n/**\n * Creates global gtag function or wraps existing one if found.\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\n * 'event' calls that belong to the GAID associated with this Firebase instance.\n *\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param dataLayerName Name of global GA datalayer array.\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\n */\nfunction wrapOrCreateGtag(initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, dataLayerName, gtagFunctionName) {\n  // Create a basic core gtag function\n  let gtagCore = function (..._args) {\n    // Must push IArguments object, not an array.\n    window[dataLayerName].push(arguments);\n  };\n  // Replace it with existing one if found\n  if (window[gtagFunctionName] && typeof window[gtagFunctionName] === 'function') {\n    // @ts-ignore\n    gtagCore = window[gtagFunctionName];\n  }\n  window[gtagFunctionName] = wrapGtag(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId);\n  return {\n    gtagCore,\n    wrappedGtag: window[gtagFunctionName]\n  };\n}\n/**\n * Returns the script tag in the DOM matching both the gtag url pattern\n * and the provided data layer name.\n */\nfunction findGtagScriptOnPage(dataLayerName) {\n  const scriptTags = window.document.getElementsByTagName('script');\n  for (const tag of Object.values(scriptTags)) {\n    if (tag.src && tag.src.includes(GTAG_URL) && tag.src.includes(dataLayerName)) {\n      return tag;\n    }\n  }\n  return null;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Backoff factor for 503 errors, which we want to be conservative about\n * to avoid overloading servers. Each retry interval will be\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\n * will be ~30 seconds (with fuzzing).\n */\nconst LONG_RETRY_FACTOR = 30;\n/**\n * Base wait interval to multiplied by backoffFactor^backoffCount.\n */\nconst BASE_INTERVAL_MILLIS = 1000;\n/**\n * Stubbable retry data storage class.\n */\nclass RetryData {\n  constructor(throttleMetadata = {}, intervalMillis = BASE_INTERVAL_MILLIS) {\n    this.throttleMetadata = throttleMetadata;\n    this.intervalMillis = intervalMillis;\n  }\n  getThrottleMetadata(appId) {\n    return this.throttleMetadata[appId];\n  }\n  setThrottleMetadata(appId, metadata) {\n    this.throttleMetadata[appId] = metadata;\n  }\n  deleteThrottleMetadata(appId) {\n    delete this.throttleMetadata[appId];\n  }\n}\nconst defaultRetryData = new RetryData();\n/**\n * Set GET request headers.\n * @param apiKey App API key.\n */\nfunction getHeaders(apiKey) {\n  return new Headers({\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n/**\n * Fetches dynamic config from backend.\n * @param app Firebase app to fetch config for.\n */\nasync function fetchDynamicConfig(appFields) {\n  const {\n    appId,\n    apiKey\n  } = appFields;\n  const request = {\n    method: 'GET',\n    headers: getHeaders(apiKey)\n  };\n  const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\n  const response = await fetch(appUrl, request);\n  if (response.status !== 200 && response.status !== 304) {\n    let errorMessage = '';\n    try {\n      // Try to get any error message text from server response.\n      const jsonResponse = await response.json();\n      if (jsonResponse.error?.message) {\n        errorMessage = jsonResponse.error.message;\n      }\n    } catch (_ignored) {}\n    throw ERROR_FACTORY.create(\"config-fetch-failed\" /* AnalyticsError.CONFIG_FETCH_FAILED */, {\n      httpStatus: response.status,\n      responseMessage: errorMessage\n    });\n  }\n  return response.json();\n}\n/**\n * Fetches dynamic config from backend, retrying if failed.\n * @param app Firebase app to fetch config for.\n */\nasync function fetchDynamicConfigWithRetry(app,\n// retryData and timeoutMillis are parameterized to allow passing a different value for testing.\nretryData = defaultRetryData, timeoutMillis) {\n  const {\n    appId,\n    apiKey,\n    measurementId\n  } = app.options;\n  if (!appId) {\n    throw ERROR_FACTORY.create(\"no-app-id\" /* AnalyticsError.NO_APP_ID */);\n  }\n  if (!apiKey) {\n    if (measurementId) {\n      return {\n        measurementId,\n        appId\n      };\n    }\n    throw ERROR_FACTORY.create(\"no-api-key\" /* AnalyticsError.NO_API_KEY */);\n  }\n  const throttleMetadata = retryData.getThrottleMetadata(appId) || {\n    backoffCount: 0,\n    throttleEndTimeMillis: Date.now()\n  };\n  const signal = new AnalyticsAbortSignal();\n  setTimeout(async () => {\n    // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n    signal.abort();\n  }, timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS);\n  return attemptFetchDynamicConfigWithRetry({\n    appId,\n    apiKey,\n    measurementId\n  }, throttleMetadata, signal, retryData);\n}\n/**\n * Runs one retry attempt.\n * @param appFields Necessary app config fields.\n * @param throttleMetadata Ongoing metadata to determine throttling times.\n * @param signal Abort signal.\n */\nasync function attemptFetchDynamicConfigWithRetry(appFields, {\n  throttleEndTimeMillis,\n  backoffCount\n}, signal, retryData = defaultRetryData // for testing\n) {\n  const {\n    appId,\n    measurementId\n  } = appFields;\n  // Starts with a (potentially zero) timeout to support resumption from stored state.\n  // Ensures the throttle end time is honored if the last attempt timed out.\n  // Note the SDK will never make a request if the fetch timeout expires at this point.\n  try {\n    await setAbortableTimeout(signal, throttleEndTimeMillis);\n  } catch (e) {\n    if (measurementId) {\n      logger.warn(`Timed out fetching this Firebase app's measurement ID from the server.` + ` Falling back to the measurement ID ${measurementId}` + ` provided in the \"measurementId\" field in the local Firebase config. [${e?.message}]`);\n      return {\n        appId,\n        measurementId\n      };\n    }\n    throw e;\n  }\n  try {\n    const response = await fetchDynamicConfig(appFields);\n    // Note the SDK only clears throttle state if response is success or non-retriable.\n    retryData.deleteThrottleMetadata(appId);\n    return response;\n  } catch (e) {\n    const error = e;\n    if (!isRetriableError(error)) {\n      retryData.deleteThrottleMetadata(appId);\n      if (measurementId) {\n        logger.warn(`Failed to fetch this Firebase app's measurement ID from the server.` + ` Falling back to the measurement ID ${measurementId}` + ` provided in the \"measurementId\" field in the local Firebase config. [${error?.message}]`);\n        return {\n          appId,\n          measurementId\n        };\n      } else {\n        throw e;\n      }\n    }\n    const backoffMillis = Number(error?.customData?.httpStatus) === 503 ? calculateBackoffMillis(backoffCount, retryData.intervalMillis, LONG_RETRY_FACTOR) : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\n    // Increments backoff state.\n    const throttleMetadata = {\n      throttleEndTimeMillis: Date.now() + backoffMillis,\n      backoffCount: backoffCount + 1\n    };\n    // Persists state.\n    retryData.setThrottleMetadata(appId, throttleMetadata);\n    logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\n    return attemptFetchDynamicConfigWithRetry(appFields, throttleMetadata, signal, retryData);\n  }\n}\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nfunction setAbortableTimeout(signal, throttleEndTimeMillis) {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n    const timeout = setTimeout(resolve, backoffMillis);\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(ERROR_FACTORY.create(\"fetch-throttle\" /* AnalyticsError.FETCH_THROTTLE */, {\n        throttleEndTimeMillis\n      }));\n    });\n  });\n}\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e) {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n  return httpStatus === 429 || httpStatus === 500 || httpStatus === 503 || httpStatus === 504;\n}\n/**\n * Shims a minimal AbortSignal (copied from Remote Config).\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nclass AnalyticsAbortSignal {\n  constructor() {\n    this.listeners = [];\n  }\n  addEventListener(listener) {\n    this.listeners.push(listener);\n  }\n  abort() {\n    this.listeners.forEach(listener => listener());\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Event parameters to set on 'gtag' during initialization.\n */\nlet defaultEventParametersForInit;\n/**\n * Logs an analytics event through the Firebase SDK.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\n * @param eventParams Analytics event parameters.\n */\nasync function logEvent$1(gtagFunction, initializationPromise, eventName, eventParams, options) {\n  if (options && options.global) {\n    gtagFunction(\"event\" /* GtagCommand.EVENT */, eventName, eventParams);\n    return;\n  } else {\n    const measurementId = await initializationPromise;\n    const params = {\n      ...eventParams,\n      'send_to': measurementId\n    };\n    gtagFunction(\"event\" /* GtagCommand.EVENT */, eventName, params);\n  }\n}\n/**\n * Set screen_name parameter for this Google Analytics ID.\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param screenName Screen name string to set.\n */\nasync function setCurrentScreen$1(gtagFunction, initializationPromise, screenName, options) {\n  if (options && options.global) {\n    gtagFunction(\"set\" /* GtagCommand.SET */, {\n      'screen_name': screenName\n    });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\n      update: true,\n      'screen_name': screenName\n    });\n  }\n}\n/**\n * Set user_id parameter for this Google Analytics ID.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param id User ID string to set\n */\nasync function setUserId$1(gtagFunction, initializationPromise, id, options) {\n  if (options && options.global) {\n    gtagFunction(\"set\" /* GtagCommand.SET */, {\n      'user_id': id\n    });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\n      update: true,\n      'user_id': id\n    });\n  }\n}\n/**\n * Set all other user properties other than user_id and screen_name.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param properties Map of user properties to set\n */\nasync function setUserProperties$1(gtagFunction, initializationPromise, properties, options) {\n  if (options && options.global) {\n    const flatProperties = {};\n    for (const key of Object.keys(properties)) {\n      // use dot notation for merge behavior in gtag.js\n      flatProperties[`user_properties.${key}`] = properties[key];\n    }\n    gtagFunction(\"set\" /* GtagCommand.SET */, flatProperties);\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\n      update: true,\n      'user_properties': properties\n    });\n  }\n}\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n */\nasync function internalGetGoogleAnalyticsClientId(gtagFunction, initializationPromise) {\n  const measurementId = await initializationPromise;\n  return new Promise((resolve, reject) => {\n    gtagFunction(\"get\" /* GtagCommand.GET */, measurementId, 'client_id', clientId => {\n      if (!clientId) {\n        reject(ERROR_FACTORY.create(\"no-client-id\" /* AnalyticsError.NO_CLIENT_ID */));\n      }\n      resolve(clientId);\n    });\n  });\n}\n/**\n * Set whether collection is enabled for this ID.\n *\n * @param enabled If true, collection is enabled for this ID.\n */\nasync function setAnalyticsCollectionEnabled$1(initializationPromise, enabled) {\n  const measurementId = await initializationPromise;\n  window[`ga-disable-${measurementId}`] = !enabled;\n}\n/**\n * Consent parameters to default to during 'gtag' initialization.\n */\nlet defaultConsentSettingsForInit;\n/**\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\n * analytics.\n *\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\n */\nfunction _setConsentDefaultForInit(consentSettings) {\n  defaultConsentSettingsForInit = consentSettings;\n}\n/**\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\n * analytics.\n *\n * @param customParams Any custom params the user may pass to gtag.js.\n */\nfunction _setDefaultEventParametersForInit(customParams) {\n  defaultEventParametersForInit = customParams;\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function validateIndexedDB() {\n  if (!isIndexedDBAvailable()) {\n    logger.warn(ERROR_FACTORY.create(\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */, {\n      errorInfo: 'IndexedDB is not available in this environment.'\n    }).message);\n    return false;\n  } else {\n    try {\n      await validateIndexedDBOpenable();\n    } catch (e) {\n      logger.warn(ERROR_FACTORY.create(\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */, {\n        errorInfo: e?.toString()\n      }).message);\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Initialize the analytics instance in gtag.js by calling config command with fid.\n *\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\n * part of the `page_view` event that's sent during the initialization\n * @param app Firebase app\n * @param gtagCore The gtag function that's not wrapped.\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\n * @param measurementIdToAppId Maps measurementID to appID.\n * @param installations _FirebaseInstallationsInternal instance.\n *\n * @returns Measurement ID.\n */\nasync function _initializeAnalytics(app, dynamicConfigPromisesList, measurementIdToAppId, installations, gtagCore, dataLayerName, options) {\n  const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\n  // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\n  dynamicConfigPromise.then(config => {\n    measurementIdToAppId[config.measurementId] = config.appId;\n    if (app.options.measurementId && config.measurementId !== app.options.measurementId) {\n      logger.warn(`The measurement ID in the local Firebase config (${app.options.measurementId})` + ` does not match the measurement ID fetched from the server (${config.measurementId}).` + ` To ensure analytics events are always sent to the correct Analytics property,` + ` update the` + ` measurement ID field in the local config or remove it from the local config.`);\n    }\n  }).catch(e => logger.error(e));\n  // Add to list to track state of all dynamic config promises.\n  dynamicConfigPromisesList.push(dynamicConfigPromise);\n  const fidPromise = validateIndexedDB().then(envIsValid => {\n    if (envIsValid) {\n      return installations.getId();\n    } else {\n      return undefined;\n    }\n  });\n  const [dynamicConfig, fid] = await Promise.all([dynamicConfigPromise, fidPromise]);\n  // Detect if user has already put the gtag <script> tag on this page with the passed in\n  // data layer name.\n  if (!findGtagScriptOnPage(dataLayerName)) {\n    insertScriptTag(dataLayerName, dynamicConfig.measurementId);\n  }\n  // Detects if there are consent settings that need to be configured.\n  if (defaultConsentSettingsForInit) {\n    gtagCore(\"consent\" /* GtagCommand.CONSENT */, 'default', defaultConsentSettingsForInit);\n    _setConsentDefaultForInit(undefined);\n  }\n  // This command initializes gtag.js and only needs to be called once for the entire web app,\n  // but since it is idempotent, we can call it multiple times.\n  // We keep it together with other initialization logic for better code structure.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  gtagCore('js', new Date());\n  // User config added first. We don't want users to accidentally overwrite\n  // base Firebase config properties.\n  const configProperties = options?.config ?? {};\n  // guard against developers accidentally setting properties with prefix `firebase_`\n  configProperties[ORIGIN_KEY] = 'firebase';\n  configProperties.update = true;\n  if (fid != null) {\n    configProperties[GA_FID_KEY] = fid;\n  }\n  // It should be the first config command called on this GA-ID\n  // Initialize this GA-ID and set FID on it using the gtag config API.\n  // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\n  // `configProperties`.\n  gtagCore(\"config\" /* GtagCommand.CONFIG */, dynamicConfig.measurementId, configProperties);\n  // Detects if there is data that will be set on every event logged from the SDK.\n  if (defaultEventParametersForInit) {\n    gtagCore(\"set\" /* GtagCommand.SET */, defaultEventParametersForInit);\n    _setDefaultEventParametersForInit(undefined);\n  }\n  return dynamicConfig.measurementId;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Analytics Service class.\n */\nclass AnalyticsService {\n  constructor(app) {\n    this.app = app;\n  }\n  _delete() {\n    delete initializationPromisesMap[this.app.options.appId];\n    return Promise.resolve();\n  }\n}\n/**\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\n * all or some of these, depending on the call's `send_to` param and the status\n * of the dynamic config fetches (see below).\n */\nlet initializationPromisesMap = {};\n/**\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\n * wait on all these to be complete in order to determine if it can selectively\n * wait for only certain initialization (FID) promises or if it must wait for all.\n */\nlet dynamicConfigPromisesList = [];\n/**\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\n * fetch completes. If already populated, gtag config calls can use this to\n * selectively wait for only this app's initialization promise (FID) instead of all\n * initialization promises.\n */\nconst measurementIdToAppId = {};\n/**\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\n */\nlet dataLayerName = 'dataLayer';\n/**\n * Name for window global gtag function used by GA: defaults to 'gtag'.\n */\nlet gtagName = 'gtag';\n/**\n * Reproduction of standard gtag function or reference to existing\n * gtag function on window object.\n */\nlet gtagCoreFunction;\n/**\n * Wrapper around gtag function that ensures FID is sent with all\n * relevant event and config calls.\n */\nlet wrappedGtagFunction;\n/**\n * Flag to ensure page initialization steps (creation or wrapping of\n * dataLayer and gtag script) are only run once per page load.\n */\nlet globalInitDone = false;\n/**\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\n * Intended to be used if `gtag.js` script has been installed on\n * this page independently of Firebase Analytics, and is using non-default\n * names for either the `gtag` function or for `dataLayer`.\n * Must be called before calling `getAnalytics()` or it won't\n * have any effect.\n *\n * @public\n *\n * @param options - Custom gtag and dataLayer names.\n */\nfunction settings(options) {\n  if (globalInitDone) {\n    throw ERROR_FACTORY.create(\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */);\n  }\n  if (options.dataLayerName) {\n    dataLayerName = options.dataLayerName;\n  }\n  if (options.gtagName) {\n    gtagName = options.gtagName;\n  }\n}\n/**\n * Returns true if no environment mismatch is found.\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\n * error that also lists details for each mismatch found.\n */\nfunction warnOnBrowserContextMismatch() {\n  const mismatchedEnvMessages = [];\n  if (isBrowserExtension()) {\n    mismatchedEnvMessages.push('This is a browser extension environment.');\n  }\n  if (!areCookiesEnabled()) {\n    mismatchedEnvMessages.push('Cookies are not available.');\n  }\n  if (mismatchedEnvMessages.length > 0) {\n    const details = mismatchedEnvMessages.map((message, index) => `(${index + 1}) ${message}`).join(' ');\n    const err = ERROR_FACTORY.create(\"invalid-analytics-context\" /* AnalyticsError.INVALID_ANALYTICS_CONTEXT */, {\n      errorInfo: details\n    });\n    logger.warn(err.message);\n  }\n}\n/**\n * Analytics instance factory.\n * @internal\n */\nfunction factory(app, installations, options) {\n  warnOnBrowserContextMismatch();\n  const appId = app.options.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(\"no-app-id\" /* AnalyticsError.NO_APP_ID */);\n  }\n  if (!app.options.apiKey) {\n    if (app.options.measurementId) {\n      logger.warn(`The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` + ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` + ` provided in the \"measurementId\" field in the local Firebase config.`);\n    } else {\n      throw ERROR_FACTORY.create(\"no-api-key\" /* AnalyticsError.NO_API_KEY */);\n    }\n  }\n  if (initializationPromisesMap[appId] != null) {\n    throw ERROR_FACTORY.create(\"already-exists\" /* AnalyticsError.ALREADY_EXISTS */, {\n      id: appId\n    });\n  }\n  if (!globalInitDone) {\n    // Steps here should only be done once per page: creation or wrapping\n    // of dataLayer and global gtag function.\n    getOrCreateDataLayer(dataLayerName);\n    const {\n      wrappedGtag,\n      gtagCore\n    } = wrapOrCreateGtag(initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, dataLayerName, gtagName);\n    wrappedGtagFunction = wrappedGtag;\n    gtagCoreFunction = gtagCore;\n    globalInitDone = true;\n  }\n  // Async but non-blocking.\n  // This map reflects the completion state of all promises for each appId.\n  initializationPromisesMap[appId] = _initializeAnalytics(app, dynamicConfigPromisesList, measurementIdToAppId, installations, gtagCoreFunction, dataLayerName, options);\n  const analyticsInstance = new AnalyticsService(app);\n  return analyticsInstance;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nfunction getAnalytics(app = getApp()) {\n  app = getModularInstance(app);\n  // Dependencies\n  const analyticsProvider = _getProvider(app, ANALYTICS_TYPE);\n  if (analyticsProvider.isInitialized()) {\n    return analyticsProvider.getImmediate();\n  }\n  return initializeAnalytics(app);\n}\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nfunction initializeAnalytics(app, options = {}) {\n  // Dependencies\n  const analyticsProvider = _getProvider(app, ANALYTICS_TYPE);\n  if (analyticsProvider.isInitialized()) {\n    const existingInstance = analyticsProvider.getImmediate();\n    if (deepEqual(options, analyticsProvider.getOptions())) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */);\n    }\n  }\n  const analyticsInstance = analyticsProvider.initialize({\n    options\n  });\n  return analyticsInstance;\n}\n/**\n * This is a public static method provided to users that wraps four different checks:\n *\n * 1. Check if it's not a browser extension environment.\n * 2. Check if cookies are enabled in current browser.\n * 3. Check if IndexedDB is supported by the browser environment.\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\n *\n * @public\n *\n */\nasync function isSupported() {\n  if (isBrowserExtension()) {\n    return false;\n  }\n  if (!areCookiesEnabled()) {\n    return false;\n  }\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n  try {\n    const isDBOpenable = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n/**\n * Use gtag `config` command to set `screen_name`.\n *\n * @public\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param screenName - Screen name to set.\n */\nfunction setCurrentScreen(analyticsInstance, screenName, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setCurrentScreen$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], screenName, options).catch(e => logger.error(e));\n}\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nasync function getGoogleAnalyticsClientId(analyticsInstance) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  return internalGetGoogleAnalyticsClientId(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId]);\n}\n/**\n * Use gtag `config` command to set `user_id`.\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param id - User ID to set.\n */\nfunction setUserId(analyticsInstance, id, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setUserId$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], id, options).catch(e => logger.error(e));\n}\n/**\n * Use gtag `config` command to set all params specified.\n *\n * @public\n */\nfunction setUserProperties(analyticsInstance, properties, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setUserProperties$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], properties, options).catch(e => logger.error(e));\n}\n/**\n * Sets whether Google Analytics collection is enabled for this app on this device.\n * Sets global `window['ga-disable-analyticsId'] = true;`\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param enabled - If true, enables collection, if false, disables it.\n */\nfunction setAnalyticsCollectionEnabled(analyticsInstance, enabled) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setAnalyticsCollectionEnabled$1(initializationPromisesMap[analyticsInstance.app.options.appId], enabled).catch(e => logger.error(e));\n}\n/**\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\n * all subsequent events.\n * @public\n * @param customParams - Any custom params the user may pass to gtag.js.\n */\nfunction setDefaultEventParameters(customParams) {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(\"set\" /* GtagCommand.SET */, customParams);\n  } else {\n    _setDefaultEventParametersForInit(customParams);\n  }\n}\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * List of official event parameters can be found in the gtag.js\n * reference documentation:\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n *\n * @public\n */\nfunction logEvent(analyticsInstance, eventName, eventParams, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  logEvent$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], eventName, eventParams, options).catch(e => logger.error(e));\n}\n/**\n * Sets the applicable end user consent state for this web app across all gtag references once\n * Firebase Analytics is initialized.\n *\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\n * types are set to \"granted\".\n * @public\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\n */\nfunction setConsent(consentSettings) {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(\"consent\" /* GtagCommand.CONSENT */, 'update', consentSettings);\n  } else {\n    _setConsentDefaultForInit(consentSettings);\n  }\n}\nconst name = \"@firebase/analytics\";\nconst version = \"0.10.18\";\n\n/**\n * The Firebase Analytics Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\nfunction registerAnalytics() {\n  _registerComponent(new Component(ANALYTICS_TYPE, (container, {\n    options: analyticsOptions\n  }) => {\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    const installations = container.getProvider('installations-internal').getImmediate();\n    return factory(app, installations, analyticsOptions);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  _registerComponent(new Component('analytics-internal', internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, 'esm2020');\n  function internalFactory(container) {\n    try {\n      const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\n      return {\n        logEvent: (eventName, eventParams, options) => logEvent(analytics, eventName, eventParams, options)\n      };\n    } catch (e) {\n      throw ERROR_FACTORY.create(\"interop-component-reg-failed\" /* AnalyticsError.INTEROP_COMPONENT_REG_FAILED */, {\n        reason: e\n      });\n    }\n  }\n}\nregisterAnalytics();\nexport { getAnalytics, getGoogleAnalyticsClientId, initializeAnalytics, isSupported, logEvent, setAnalyticsCollectionEnabled, setConsent, setCurrentScreen, setDefaultEventParameters, setUserId, setUserProperties, settings };", "map": {"version": 3, "names": ["ANALYTICS_TYPE", "GA_FID_KEY", "ORIGIN_KEY", "FETCH_TIMEOUT_MILLIS", "DYNAMIC_CONFIG_URL", "GTAG_URL", "logger", "<PERSON><PERSON>", "ERRORS", "ERROR_FACTORY", "ErrorFactory", "createGtagTrustedTypesScriptURL", "url", "startsWith", "err", "create", "gtagURL", "warn", "message", "promiseAllSettled", "promises", "Promise", "all", "map", "promise", "catch", "e", "createTrustedTypesPolicy", "policyName", "policyOptions", "trustedTypesPolicy", "window", "trustedTypes", "createPolicy", "insertScriptTag", "dataLayerName", "measurementId", "createScriptURL", "script", "document", "createElement", "gtagScriptURL", "src", "async", "head", "append<PERSON><PERSON><PERSON>", "getOrCreateDataLayer", "dataLayer", "Array", "isArray", "gtagOnConfig", "gtagCore", "initializationPromisesMap", "dynamicConfigPromisesList", "measurementIdToAppId", "gtagParams", "correspondingAppId", "dynamicConfigResults", "foundConfig", "find", "config", "appId", "error", "gtagOnEvent", "initializationPromisesToWaitFor", "gaSendToList", "sendToId", "initializationPromise", "push", "length", "Object", "values", "wrapGtag", "gtagWrapper", "command", "args", "consentAction", "fieldName", "callback", "customParams", "wrapOrCreateGtag", "gtagFunctionName", "_args", "arguments", "wrappedGtag", "findGtagScriptOnPage", "scriptTags", "getElementsByTagName", "tag", "includes", "LONG_RETRY_FACTOR", "BASE_INTERVAL_MILLIS", "RetryData", "constructor", "throttleMetadata", "<PERSON><PERSON><PERSON><PERSON>", "getThrottleMetadata", "setThrottleMetadata", "metadata", "deleteThrottleMetadata", "defaultRetryData", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "fetchDynamicConfig", "appFields", "request", "method", "headers", "appUrl", "replace", "response", "fetch", "status", "errorMessage", "jsonResponse", "json", "_ignored", "httpStatus", "responseMessage", "fetchDynamicConfigWithRetry", "app", "retryData", "timeout<PERSON><PERSON><PERSON>", "options", "backoffCount", "throttleEnd<PERSON>imeMill<PERSON>", "Date", "now", "signal", "AnalyticsAbortSignal", "setTimeout", "abort", "undefined", "attemptFetchDynamicConfigWithRetry", "setAbortableTimeout", "isRetriableError", "backoff<PERSON><PERSON><PERSON>", "Number", "customData", "calculateBackoffMillis", "debug", "resolve", "reject", "Math", "max", "timeout", "addEventListener", "clearTimeout", "FirebaseError", "listeners", "listener", "for<PERSON>ach", "defaultEventParametersForInit", "logEvent$1", "logEvent", "gtagFunction", "eventName", "eventParams", "global", "params", "setCurrentScreen$1", "setCurrentScreen", "screenName", "update", "setUserId$1", "setUserId", "id", "setUserProperties$1", "setUserProperties", "properties", "flatProperties", "key", "keys", "internalGetGoogleAnalyticsClientId", "clientId", "setAnalyticsCollectionEnabled$1", "setAnalyticsCollectionEnabled", "enabled", "defaultConsentSettingsForInit", "_setConsentDefaultForInit", "consentSettings", "_setDefaultEventParametersForInit", "validateIndexedDB", "isIndexedDBAvailable", "errorInfo", "validateIndexedDBOpenable", "toString", "_initializeAnalytics", "installations", "dynamicConfigPromise", "then", "fidPromise", "envIsValid", "getId", "dynamicConfig", "fid", "configProperties", "AnalyticsService", "_delete", "gtagName", "gtagCoreFunction", "wrappedGtagFunction", "globalInitDone", "settings", "warnOnBrowserContextMismatch", "mismatchedEnvMessages", "isBrowserExtension", "areCookiesEnabled", "details", "index", "join", "factory", "analyticsInstance", "getAnalytics", "getApp", "getModularInstance", "analyticsProvider", "_get<PERSON><PERSON><PERSON>", "isInitialized", "getImmediate", "initializeAnalytics", "existingInstance", "deepEqual", "getOptions", "initialize", "isSupported", "isDBOpenable", "getGoogleAnalyticsClientId", "setDefaultEventParameters", "setConsent", "registerAnalytics", "_registerComponent", "Component", "container", "analyticsOptions", "get<PERSON><PERSON><PERSON>", "internalFactory", "registerVersion", "name", "version", "analytics", "reason"], "sources": ["C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\constants.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\logger.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\errors.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\helpers.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\get-config.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\functions.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\initialize-analytics.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\factory.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\api.ts", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\node_modules\\@firebase\\analytics\\src\\index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Analytics.\n */\nexport const ANALYTICS_TYPE = 'analytics';\n\n// Key to attach FID to in gtag params.\nexport const GA_FID_KEY = 'firebase_id';\nexport const ORIGIN_KEY = 'origin';\n\nexport const FETCH_TIMEOUT_MILLIS = 60 * 1000;\n\nexport const DYNAMIC_CONFIG_URL =\n  'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\n\nexport const GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\nexport const enum GtagCommand {\n  EVENT = 'event',\n  SET = 'set',\n  CONFIG = 'config',\n  CONSENT = 'consent',\n  GET = 'get'\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/analytics');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AnalyticsError {\n  ALREADY_EXISTS = 'already-exists',\n  ALREADY_INITIALIZED = 'already-initialized',\n  ALREADY_INITIALIZED_SETTINGS = 'already-initialized-settings',\n  INTEROP_COMPONENT_REG_FAILED = 'interop-component-reg-failed',\n  INVALID_ANALYTICS_CONTEXT = 'invalid-analytics-context',\n  INDEXEDDB_UNAVAILABLE = 'indexeddb-unavailable',\n  FETCH_THROTTLE = 'fetch-throttle',\n  CONFIG_FETCH_FAILED = 'config-fetch-failed',\n  NO_API_KEY = 'no-api-key',\n  NO_APP_ID = 'no-app-id',\n  NO_CLIENT_ID = 'no-client-id',\n  INVALID_GTAG_RESOURCE = 'invalid-gtag-resource'\n}\n\nconst ERRORS: ErrorMap<AnalyticsError> = {\n  [AnalyticsError.ALREADY_EXISTS]:\n    'A Firebase Analytics instance with the appId {$id} ' +\n    ' already exists. ' +\n    'Only one Firebase Analytics instance can be created for each appId.',\n  [AnalyticsError.ALREADY_INITIALIZED]:\n    'initializeAnalytics() cannot be called again with different options than those ' +\n    'it was initially called with. It can be called again with the same options to ' +\n    'return the existing instance, or getAnalytics() can be used ' +\n    'to get a reference to the already-initialized instance.',\n  [AnalyticsError.ALREADY_INITIALIZED_SETTINGS]:\n    'Firebase Analytics has already been initialized.' +\n    'settings() must be called before initializing any Analytics instance' +\n    'or it will have no effect.',\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]:\n    'Firebase Analytics Interop Component failed to instantiate: {$reason}',\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]:\n    'Firebase Analytics is not supported in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]:\n    'IndexedDB unavailable or restricted in this environment. ' +\n    'Wrap initialization of analytics in analytics.isSupported() ' +\n    'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [AnalyticsError.FETCH_THROTTLE]:\n    'The config fetch request timed out while in an exponential backoff state.' +\n    ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [AnalyticsError.CONFIG_FETCH_FAILED]:\n    'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\n  [AnalyticsError.NO_API_KEY]:\n    'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid API key.',\n  [AnalyticsError.NO_APP_ID]:\n    'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\n    'contain a valid app ID.',\n  [AnalyticsError.NO_CLIENT_ID]: 'The \"client_id\" field is empty.',\n  [AnalyticsError.INVALID_GTAG_RESOURCE]:\n    'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\n};\n\ninterface ErrorParams {\n  [AnalyticsError.ALREADY_EXISTS]: { id: string };\n  [AnalyticsError.INTEROP_COMPONENT_REG_FAILED]: { reason: Error };\n  [AnalyticsError.FETCH_THROTTLE]: { throttleEndTimeMillis: number };\n  [AnalyticsError.CONFIG_FETCH_FAILED]: {\n    httpStatus: number;\n    responseMessage: string;\n  };\n  [AnalyticsError.INVALID_ANALYTICS_CONTEXT]: { errorInfo: string };\n  [AnalyticsError.INDEXEDDB_UNAVAILABLE]: { errorInfo: string };\n  [AnalyticsError.INVALID_GTAG_RESOURCE]: { gtagURL: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AnalyticsError, ErrorParams>(\n  'analytics',\n  'Analytics',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { DynamicConfig, DataLayer, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GTAG_URL } from './constants';\nimport { logger } from './logger';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n// Possible parameter types for gtag 'event' and 'config' commands\ntype GtagConfigOrEventParams = ControlParams & EventParams & CustomParams;\n\n/**\n * Verifies and creates a TrustedScriptURL.\n */\nexport function createGtagTrustedTypesScriptURL(url: string): string {\n  if (!url.startsWith(GTAG_URL)) {\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_GTAG_RESOURCE, {\n      gtagURL: url\n    });\n    logger.warn(err.message);\n    return '';\n  }\n  return url;\n}\n\n/**\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\n * have either resolved or rejected.\n *\n * @param promises Array of promises to wait for.\n */\nexport function promiseAllSettled<T>(\n  promises: Array<Promise<T>>\n): Promise<T[]> {\n  return Promise.all(promises.map(promise => promise.catch(e => e)));\n}\n\n/**\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\n *\n * @param policyName A string containing the name of the policy\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\n * | the TrustedTypePolicy reference documentation}.\n */\nexport function createTrustedTypesPolicy(\n  policyName: string,\n  policyOptions: Partial<TrustedTypePolicyOptions>\n): Partial<TrustedTypePolicy> | undefined {\n  // Create a TrustedTypes policy that we can use for updating src\n  // properties\n  let trustedTypesPolicy: Partial<TrustedTypePolicy> | undefined;\n  if (window.trustedTypes) {\n    trustedTypesPolicy = window.trustedTypes.createPolicy(\n      policyName,\n      policyOptions\n    );\n  }\n  return trustedTypesPolicy;\n}\n\n/**\n * Inserts gtag script tag into the page to asynchronously download gtag.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function insertScriptTag(\n  dataLayerName: string,\n  measurementId: string\n): void {\n  const trustedTypesPolicy = createTrustedTypesPolicy(\n    'firebase-js-sdk-policy',\n    {\n      createScriptURL: createGtagTrustedTypesScriptURL\n    }\n  );\n\n  const script = document.createElement('script');\n  // We are not providing an analyticsId in the URL because it would trigger a `page_view`\n  // without fid. We will initialize ga-id using gtag (config) command together with fid.\n\n  const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\n  (script.src as string | TrustedScriptURL) = trustedTypesPolicy\n    ? (trustedTypesPolicy as TrustedTypePolicy)?.createScriptURL(gtagScriptURL)\n    : gtagScriptURL;\n\n  script.async = true;\n  document.head.appendChild(script);\n}\n\n/**\n * Get reference to, or create, global datalayer.\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\n */\nexport function getOrCreateDataLayer(dataLayerName: string): DataLayer {\n  // Check for existing dataLayer and create if needed.\n  let dataLayer: DataLayer = [];\n  if (Array.isArray(window[dataLayerName])) {\n    dataLayer = window[dataLayerName] as DataLayer;\n  } else {\n    window[dataLayerName] = dataLayer;\n  }\n  return dataLayer;\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'config' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param measurementId GA Measurement ID to set config for.\n * @param gtagParams Gtag config params to set.\n */\nasync function gtagOnConfig(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  // If config is already fetched, we know the appId and can use it to look up what FID promise we\n  /// are waiting for, and wait only on that one.\n  const correspondingAppId = measurementIdToAppId[measurementId as string];\n  try {\n    if (correspondingAppId) {\n      await initializationPromisesMap[correspondingAppId];\n    } else {\n      // If config is not fetched yet, wait for all configs (we don't know which one we need) and\n      // find the appId (if any) corresponding to this measurementId. If there is one, wait on\n      // that appId's initialization promise. If there is none, promise resolves and gtag\n      // call goes through.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      const foundConfig = dynamicConfigResults.find(\n        config => config.measurementId === measurementId\n      );\n      if (foundConfig) {\n        await initializationPromisesMap[foundConfig.appId];\n      }\n    }\n  } catch (e) {\n    logger.error(e);\n  }\n  gtagCore(GtagCommand.CONFIG, measurementId, gtagParams);\n}\n\n/**\n * Wrapped gtag logic when gtag is called with 'event' command.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementId GA Measurement ID to log event to.\n * @param gtagParams Params to log with this event.\n */\nasync function gtagOnEvent(\n  gtagCore: Gtag,\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementId: string,\n  gtagParams?: ControlParams & EventParams & CustomParams\n): Promise<void> {\n  try {\n    let initializationPromisesToWaitFor: Array<Promise<string>> = [];\n\n    // If there's a 'send_to' param, check if any ID specified matches\n    // an initializeIds() promise we are waiting for.\n    if (gtagParams && gtagParams['send_to']) {\n      let gaSendToList: string | string[] = gtagParams['send_to'];\n      // Make it an array if is isn't, so it can be dealt with the same way.\n      if (!Array.isArray(gaSendToList)) {\n        gaSendToList = [gaSendToList];\n      }\n      // Checking 'send_to' fields requires having all measurement ID results back from\n      // the dynamic config fetch.\n      const dynamicConfigResults = await promiseAllSettled(\n        dynamicConfigPromisesList\n      );\n      for (const sendToId of gaSendToList) {\n        // Any fetched dynamic measurement ID that matches this 'send_to' ID\n        const foundConfig = dynamicConfigResults.find(\n          config => config.measurementId === sendToId\n        );\n        const initializationPromise =\n          foundConfig && initializationPromisesMap[foundConfig.appId];\n        if (initializationPromise) {\n          initializationPromisesToWaitFor.push(initializationPromise);\n        } else {\n          // Found an item in 'send_to' that is not associated\n          // directly with an FID, possibly a group.  Empty this array,\n          // exit the loop early, and let it get populated below.\n          initializationPromisesToWaitFor = [];\n          break;\n        }\n      }\n    }\n\n    // This will be unpopulated if there was no 'send_to' field , or\n    // if not all entries in the 'send_to' field could be mapped to\n    // a FID. In these cases, wait on all pending initialization promises.\n    if (initializationPromisesToWaitFor.length === 0) {\n      /* eslint-disable-next-line @typescript-eslint/no-floating-promises */\n      initializationPromisesToWaitFor = Object.values(\n        initializationPromisesMap\n      );\n    }\n\n    // Run core gtag function with args after all relevant initialization\n    // promises have been resolved.\n    await Promise.all(initializationPromisesToWaitFor);\n    // Workaround for http://b/141370449 - third argument cannot be undefined.\n    gtagCore(GtagCommand.EVENT, measurementId, gtagParams || {});\n  } catch (e) {\n    logger.error(e);\n  }\n}\n\n/**\n * Wraps a standard gtag function with extra code to wait for completion of\n * relevant initialization promises before sending requests.\n *\n * @param gtagCore Basic gtag function that just appends to dataLayer.\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n */\nfunction wrapGtag(\n  gtagCore: Gtag,\n  /**\n   * Allows wrapped gtag calls to wait on whichever initialization promises are required,\n   * depending on the contents of the gtag params' `send_to` field, if any.\n   */\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  /**\n   * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\n   * before determining what initialization promises (which include FIDs) to wait for.\n   */\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  /**\n   * Wrapped gtag config calls can narrow down which initialization promise (with FID)\n   * to wait for if the measurementId is already fetched, by getting the corresponding appId,\n   * which is the key for the initialization promises map.\n   */\n  measurementIdToAppId: { [measurementId: string]: string }\n): Gtag {\n  /**\n   * Wrapper around gtag that ensures FID is sent with gtag calls.\n   * @param command Gtag command type.\n   * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\n   * @param gtagParams Params if event is EVENT/CONFIG.\n   */\n  async function gtagWrapper(\n    command: 'config' | 'set' | 'event' | 'consent' | 'get' | string,\n    ...args: unknown[]\n  ): Promise<void> {\n    try {\n      // If event, check that relevant initialization promises have completed.\n      if (command === GtagCommand.EVENT) {\n        const [measurementId, gtagParams] = args;\n        // If EVENT, second arg must be measurementId.\n        await gtagOnEvent(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONFIG) {\n        const [measurementId, gtagParams] = args;\n        // If CONFIG, second arg must be measurementId.\n        await gtagOnConfig(\n          gtagCore,\n          initializationPromisesMap,\n          dynamicConfigPromisesList,\n          measurementIdToAppId,\n          measurementId as string,\n          gtagParams as GtagConfigOrEventParams\n        );\n      } else if (command === GtagCommand.CONSENT) {\n        const [consentAction, gtagParams] = args;\n        // consentAction can be one of 'default' or 'update'.\n        gtagCore(\n          GtagCommand.CONSENT,\n          consentAction,\n          gtagParams as ConsentSettings\n        );\n      } else if (command === GtagCommand.GET) {\n        const [measurementId, fieldName, callback] = args;\n        gtagCore(\n          GtagCommand.GET,\n          measurementId as string,\n          fieldName as string,\n          callback as (...args: unknown[]) => void\n        );\n      } else if (command === GtagCommand.SET) {\n        const [customParams] = args;\n        // If SET, second arg must be params.\n        gtagCore(GtagCommand.SET, customParams as CustomParams);\n      } else {\n        gtagCore(command, ...args);\n      }\n    } catch (e) {\n      logger.error(e);\n    }\n  }\n  return gtagWrapper as Gtag;\n}\n\n/**\n * Creates global gtag function or wraps existing one if found.\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\n * 'event' calls that belong to the GAID associated with this Firebase instance.\n *\n * @param initializationPromisesMap Map of appIds to their initialization promises.\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\n * @param dataLayerName Name of global GA datalayer array.\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\n */\nexport function wrapOrCreateGtag(\n  initializationPromisesMap: { [appId: string]: Promise<string> },\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [measurementId: string]: string },\n  dataLayerName: string,\n  gtagFunctionName: string\n): {\n  gtagCore: Gtag;\n  wrappedGtag: Gtag;\n} {\n  // Create a basic core gtag function\n  let gtagCore: Gtag = function (..._args: unknown[]) {\n    // Must push IArguments object, not an array.\n    (window[dataLayerName] as DataLayer).push(arguments);\n  };\n\n  // Replace it with existing one if found\n  if (\n    window[gtagFunctionName] &&\n    typeof window[gtagFunctionName] === 'function'\n  ) {\n    // @ts-ignore\n    gtagCore = window[gtagFunctionName];\n  }\n\n  window[gtagFunctionName] = wrapGtag(\n    gtagCore,\n    initializationPromisesMap,\n    dynamicConfigPromisesList,\n    measurementIdToAppId\n  );\n\n  return {\n    gtagCore,\n    wrappedGtag: window[gtagFunctionName] as Gtag\n  };\n}\n\n/**\n * Returns the script tag in the DOM matching both the gtag url pattern\n * and the provided data layer name.\n */\nexport function findGtagScriptOnPage(\n  dataLayerName: string\n): HTMLScriptElement | null {\n  const scriptTags = window.document.getElementsByTagName('script');\n  for (const tag of Object.values(scriptTags)) {\n    if (\n      tag.src &&\n      tag.src.includes(GTAG_URL) &&\n      tag.src.includes(dataLayerName)\n    ) {\n      return tag;\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * @fileoverview Most logic is copied from packages/remote-config/src/client/retrying_client.ts\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { DynamicConfig, ThrottleMetadata, MinimalDynamicConfig } from './types';\nimport { FirebaseError, calculateBackoffMillis } from '@firebase/util';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { DYNAMIC_CONFIG_URL, FETCH_TIMEOUT_MILLIS } from './constants';\nimport { logger } from './logger';\n\n// App config fields needed by analytics.\nexport interface AppFields {\n  appId: string;\n  apiKey: string;\n  measurementId?: string;\n}\n\n/**\n * Backoff factor for 503 errors, which we want to be conservative about\n * to avoid overloading servers. Each retry interval will be\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\n * will be ~30 seconds (with fuzzing).\n */\nexport const LONG_RETRY_FACTOR = 30;\n\n/**\n * Base wait interval to multiplied by backoffFactor^backoffCount.\n */\nconst BASE_INTERVAL_MILLIS = 1000;\n\n/**\n * Stubbable retry data storage class.\n */\nclass RetryData {\n  constructor(\n    public throttleMetadata: { [appId: string]: ThrottleMetadata } = {},\n    public intervalMillis: number = BASE_INTERVAL_MILLIS\n  ) {}\n\n  getThrottleMetadata(appId: string): ThrottleMetadata {\n    return this.throttleMetadata[appId];\n  }\n\n  setThrottleMetadata(appId: string, metadata: ThrottleMetadata): void {\n    this.throttleMetadata[appId] = metadata;\n  }\n\n  deleteThrottleMetadata(appId: string): void {\n    delete this.throttleMetadata[appId];\n  }\n}\n\nconst defaultRetryData = new RetryData();\n\n/**\n * Set GET request headers.\n * @param apiKey App API key.\n */\nfunction getHeaders(apiKey: string): Headers {\n  return new Headers({\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\n/**\n * Fetches dynamic config from backend.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfig(\n  appFields: AppFields\n): Promise<DynamicConfig> {\n  const { appId, apiKey } = appFields;\n  const request: RequestInit = {\n    method: 'GET',\n    headers: getHeaders(apiKey)\n  };\n  const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\n  const response = await fetch(appUrl, request);\n  if (response.status !== 200 && response.status !== 304) {\n    let errorMessage = '';\n    try {\n      // Try to get any error message text from server response.\n      const jsonResponse = (await response.json()) as {\n        error?: { message?: string };\n      };\n      if (jsonResponse.error?.message) {\n        errorMessage = jsonResponse.error.message;\n      }\n    } catch (_ignored) {}\n    throw ERROR_FACTORY.create(AnalyticsError.CONFIG_FETCH_FAILED, {\n      httpStatus: response.status,\n      responseMessage: errorMessage\n    });\n  }\n  return response.json();\n}\n\n/**\n * Fetches dynamic config from backend, retrying if failed.\n * @param app Firebase app to fetch config for.\n */\nexport async function fetchDynamicConfigWithRetry(\n  app: FirebaseApp,\n  // retryData and timeoutMillis are parameterized to allow passing a different value for testing.\n  retryData: RetryData = defaultRetryData,\n  timeoutMillis?: number\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, apiKey, measurementId } = app.options;\n\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n\n  if (!apiKey) {\n    if (measurementId) {\n      return {\n        measurementId,\n        appId\n      };\n    }\n    throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n  }\n\n  const throttleMetadata: ThrottleMetadata = retryData.getThrottleMetadata(\n    appId\n  ) || {\n    backoffCount: 0,\n    throttleEndTimeMillis: Date.now()\n  };\n\n  const signal = new AnalyticsAbortSignal();\n\n  setTimeout(\n    async () => {\n      // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n      signal.abort();\n    },\n    timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS\n  );\n\n  return attemptFetchDynamicConfigWithRetry(\n    { appId, apiKey, measurementId },\n    throttleMetadata,\n    signal,\n    retryData\n  );\n}\n\n/**\n * Runs one retry attempt.\n * @param appFields Necessary app config fields.\n * @param throttleMetadata Ongoing metadata to determine throttling times.\n * @param signal Abort signal.\n */\nasync function attemptFetchDynamicConfigWithRetry(\n  appFields: AppFields,\n  { throttleEndTimeMillis, backoffCount }: ThrottleMetadata,\n  signal: AnalyticsAbortSignal,\n  retryData: RetryData = defaultRetryData // for testing\n): Promise<DynamicConfig | MinimalDynamicConfig> {\n  const { appId, measurementId } = appFields;\n  // Starts with a (potentially zero) timeout to support resumption from stored state.\n  // Ensures the throttle end time is honored if the last attempt timed out.\n  // Note the SDK will never make a request if the fetch timeout expires at this point.\n  try {\n    await setAbortableTimeout(signal, throttleEndTimeMillis);\n  } catch (e) {\n    if (measurementId) {\n      logger.warn(\n        `Timed out fetching this Firebase app's measurement ID from the server.` +\n          ` Falling back to the measurement ID ${measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config. [${\n            (e as Error)?.message\n          }]`\n      );\n      return { appId, measurementId };\n    }\n    throw e;\n  }\n\n  try {\n    const response = await fetchDynamicConfig(appFields);\n\n    // Note the SDK only clears throttle state if response is success or non-retriable.\n    retryData.deleteThrottleMetadata(appId);\n\n    return response;\n  } catch (e) {\n    const error = e as Error;\n    if (!isRetriableError(error)) {\n      retryData.deleteThrottleMetadata(appId);\n      if (measurementId) {\n        logger.warn(\n          `Failed to fetch this Firebase app's measurement ID from the server.` +\n            ` Falling back to the measurement ID ${measurementId}` +\n            ` provided in the \"measurementId\" field in the local Firebase config. [${error?.message}]`\n        );\n        return { appId, measurementId };\n      } else {\n        throw e;\n      }\n    }\n\n    const backoffMillis =\n      Number(error?.customData?.httpStatus) === 503\n        ? calculateBackoffMillis(\n            backoffCount,\n            retryData.intervalMillis,\n            LONG_RETRY_FACTOR\n          )\n        : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\n\n    // Increments backoff state.\n    const throttleMetadata = {\n      throttleEndTimeMillis: Date.now() + backoffMillis,\n      backoffCount: backoffCount + 1\n    };\n\n    // Persists state.\n    retryData.setThrottleMetadata(appId, throttleMetadata);\n    logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\n\n    return attemptFetchDynamicConfigWithRetry(\n      appFields,\n      throttleMetadata,\n      signal,\n      retryData\n    );\n  }\n}\n\n/**\n * Supports waiting on a backoff by:\n *\n * <ul>\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\n *       request appear the same.</li>\n * </ul>\n *\n * <p>Visible for testing.\n */\nfunction setAbortableTimeout(\n  signal: AnalyticsAbortSignal,\n  throttleEndTimeMillis: number\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n\n    const timeout = setTimeout(resolve, backoffMillis);\n\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(\n        ERROR_FACTORY.create(AnalyticsError.FETCH_THROTTLE, {\n          throttleEndTimeMillis\n        })\n      );\n    });\n  });\n}\n\ntype RetriableError = FirebaseError & { customData: { httpStatus: string } };\n\n/**\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\n */\nfunction isRetriableError(e: Error): e is RetriableError {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n\n  return (\n    httpStatus === 429 ||\n    httpStatus === 500 ||\n    httpStatus === 503 ||\n    httpStatus === 504\n  );\n}\n\n/**\n * Shims a minimal AbortSignal (copied from Remote Config).\n *\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\n * swapped out if/when we do.\n */\nexport class AnalyticsAbortSignal {\n  listeners: Array<() => void> = [];\n  addEventListener(listener: () => void): void {\n    this.listeners.push(listener);\n  }\n  abort(): void {\n    this.listeners.forEach(listener => listener());\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AnalyticsCallOptions,\n  CustomParams,\n  ControlParams,\n  EventParams,\n  ConsentSettings\n} from './public-types';\nimport { Gtag } from './types';\nimport { GtagCommand } from './constants';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\n\n/**\n * Event parameters to set on 'gtag' during initialization.\n */\nexport let defaultEventParametersForInit: CustomParams | undefined;\n\n/**\n * Logs an analytics event through the Firebase SDK.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\n * @param eventParams Analytics event parameters.\n */\nexport async function logEvent(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.EVENT, eventName, eventParams);\n    return;\n  } else {\n    const measurementId = await initializationPromise;\n    const params: EventParams | ControlParams = {\n      ...eventParams,\n      'send_to': measurementId\n    };\n    gtagFunction(GtagCommand.EVENT, eventName, params);\n  }\n}\n\n/**\n * Set screen_name parameter for this Google Analytics ID.\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param screenName Screen name string to set.\n */\nexport async function setCurrentScreen(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  screenName: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'screen_name': screenName });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'screen_name': screenName\n    });\n  }\n}\n\n/**\n * Set user_id parameter for this Google Analytics ID.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param id User ID string to set\n */\nexport async function setUserId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    gtagFunction(GtagCommand.SET, { 'user_id': id });\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_id': id\n    });\n  }\n}\n\n/**\n * Set all other user properties other than user_id and screen_name.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n * @param properties Map of user properties to set\n */\nexport async function setUserProperties(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): Promise<void> {\n  if (options && options.global) {\n    const flatProperties: { [key: string]: unknown } = {};\n    for (const key of Object.keys(properties)) {\n      // use dot notation for merge behavior in gtag.js\n      flatProperties[`user_properties.${key}`] = properties[key];\n    }\n    gtagFunction(GtagCommand.SET, flatProperties);\n    return Promise.resolve();\n  } else {\n    const measurementId = await initializationPromise;\n    gtagFunction(GtagCommand.CONFIG, measurementId, {\n      update: true,\n      'user_properties': properties\n    });\n  }\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\n */\nexport async function internalGetGoogleAnalyticsClientId(\n  gtagFunction: Gtag,\n  initializationPromise: Promise<string>\n): Promise<string> {\n  const measurementId = await initializationPromise;\n  return new Promise((resolve, reject) => {\n    gtagFunction(\n      GtagCommand.GET,\n      measurementId,\n      'client_id',\n      (clientId: string) => {\n        if (!clientId) {\n          reject(ERROR_FACTORY.create(AnalyticsError.NO_CLIENT_ID));\n        }\n        resolve(clientId);\n      }\n    );\n  });\n}\n\n/**\n * Set whether collection is enabled for this ID.\n *\n * @param enabled If true, collection is enabled for this ID.\n */\nexport async function setAnalyticsCollectionEnabled(\n  initializationPromise: Promise<string>,\n  enabled: boolean\n): Promise<void> {\n  const measurementId = await initializationPromise;\n  window[`ga-disable-${measurementId}`] = !enabled;\n}\n\n/**\n * Consent parameters to default to during 'gtag' initialization.\n */\nexport let defaultConsentSettingsForInit: ConsentSettings | undefined;\n\n/**\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\n * analytics.\n *\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\n */\nexport function _setConsentDefaultForInit(\n  consentSettings?: ConsentSettings\n): void {\n  defaultConsentSettingsForInit = consentSettings;\n}\n\n/**\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\n * analytics.\n *\n * @param customParams Any custom params the user may pass to gtag.js.\n */\nexport function _setDefaultEventParametersForInit(\n  customParams?: CustomParams\n): void {\n  defaultEventParametersForInit = customParams;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DynamicConfig, Gtag, MinimalDynamicConfig } from './types';\nimport { GtagCommand, GA_FID_KEY, ORIGIN_KEY } from './constants';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { fetchDynamicConfigWithRetry } from './get-config';\nimport { logger } from './logger';\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { findGtagScriptOnPage, insertScriptTag } from './helpers';\nimport { AnalyticsSettings } from './public-types';\nimport {\n  defaultConsentSettingsForInit,\n  _setConsentDefaultForInit,\n  defaultEventParametersForInit,\n  _setDefaultEventParametersForInit\n} from './functions';\n\nasync function validateIndexedDB(): Promise<boolean> {\n  if (!isIndexedDBAvailable()) {\n    logger.warn(\n      ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n        errorInfo: 'IndexedDB is not available in this environment.'\n      }).message\n    );\n    return false;\n  } else {\n    try {\n      await validateIndexedDBOpenable();\n    } catch (e) {\n      logger.warn(\n        ERROR_FACTORY.create(AnalyticsError.INDEXEDDB_UNAVAILABLE, {\n          errorInfo: (e as Error)?.toString()\n        }).message\n      );\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Initialize the analytics instance in gtag.js by calling config command with fid.\n *\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\n * part of the `page_view` event that's sent during the initialization\n * @param app Firebase app\n * @param gtagCore The gtag function that's not wrapped.\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\n * @param measurementIdToAppId Maps measurementID to appID.\n * @param installations _FirebaseInstallationsInternal instance.\n *\n * @returns Measurement ID.\n */\nexport async function _initializeAnalytics(\n  app: FirebaseApp,\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >,\n  measurementIdToAppId: { [key: string]: string },\n  installations: _FirebaseInstallationsInternal,\n  gtagCore: Gtag,\n  dataLayerName: string,\n  options?: AnalyticsSettings\n): Promise<string> {\n  const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\n  // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\n  dynamicConfigPromise\n    .then(config => {\n      measurementIdToAppId[config.measurementId] = config.appId;\n      if (\n        app.options.measurementId &&\n        config.measurementId !== app.options.measurementId\n      ) {\n        logger.warn(\n          `The measurement ID in the local Firebase config (${app.options.measurementId})` +\n            ` does not match the measurement ID fetched from the server (${config.measurementId}).` +\n            ` To ensure analytics events are always sent to the correct Analytics property,` +\n            ` update the` +\n            ` measurement ID field in the local config or remove it from the local config.`\n        );\n      }\n    })\n    .catch(e => logger.error(e));\n  // Add to list to track state of all dynamic config promises.\n  dynamicConfigPromisesList.push(dynamicConfigPromise);\n\n  const fidPromise: Promise<string | undefined> = validateIndexedDB().then(\n    envIsValid => {\n      if (envIsValid) {\n        return installations.getId();\n      } else {\n        return undefined;\n      }\n    }\n  );\n\n  const [dynamicConfig, fid] = await Promise.all([\n    dynamicConfigPromise,\n    fidPromise\n  ]);\n\n  // Detect if user has already put the gtag <script> tag on this page with the passed in\n  // data layer name.\n  if (!findGtagScriptOnPage(dataLayerName)) {\n    insertScriptTag(dataLayerName, dynamicConfig.measurementId);\n  }\n\n  // Detects if there are consent settings that need to be configured.\n  if (defaultConsentSettingsForInit) {\n    gtagCore(GtagCommand.CONSENT, 'default', defaultConsentSettingsForInit);\n    _setConsentDefaultForInit(undefined);\n  }\n\n  // This command initializes gtag.js and only needs to be called once for the entire web app,\n  // but since it is idempotent, we can call it multiple times.\n  // We keep it together with other initialization logic for better code structure.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  (gtagCore as any)('js', new Date());\n  // User config added first. We don't want users to accidentally overwrite\n  // base Firebase config properties.\n  const configProperties: Record<string, unknown> = options?.config ?? {};\n\n  // guard against developers accidentally setting properties with prefix `firebase_`\n  configProperties[ORIGIN_KEY] = 'firebase';\n  configProperties.update = true;\n\n  if (fid != null) {\n    configProperties[GA_FID_KEY] = fid;\n  }\n\n  // It should be the first config command called on this GA-ID\n  // Initialize this GA-ID and set FID on it using the gtag config API.\n  // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\n  // `configProperties`.\n  gtagCore(GtagCommand.CONFIG, dynamicConfig.measurementId, configProperties);\n\n  // Detects if there is data that will be set on every event logged from the SDK.\n  if (defaultEventParametersForInit) {\n    gtagCore(GtagCommand.SET, defaultEventParametersForInit);\n    _setDefaultEventParametersForInit(undefined);\n  }\n\n  return dynamicConfig.measurementId;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsOptions, Analytics, AnalyticsSettings } from './public-types';\nimport { Gtag, DynamicConfig, MinimalDynamicConfig } from './types';\nimport { getOrCreateDataLayer, wrapOrCreateGtag } from './helpers';\nimport { AnalyticsError, ERROR_FACTORY } from './errors';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { areCookiesEnabled, isBrowserExtension } from '@firebase/util';\nimport { _initializeAnalytics } from './initialize-analytics';\nimport { logger } from './logger';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\n\n/**\n * Analytics Service class.\n */\nexport class AnalyticsService implements Analytics, _FirebaseService {\n  constructor(public app: FirebaseApp) {}\n  _delete(): Promise<void> {\n    delete initializationPromisesMap[this.app.options.appId!];\n    return Promise.resolve();\n  }\n}\n\n/**\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\n * all or some of these, depending on the call's `send_to` param and the status\n * of the dynamic config fetches (see below).\n */\nexport let initializationPromisesMap: {\n  [appId: string]: Promise<string>; // Promise contains measurement ID string.\n} = {};\n\n/**\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\n * wait on all these to be complete in order to determine if it can selectively\n * wait for only certain initialization (FID) promises or if it must wait for all.\n */\nlet dynamicConfigPromisesList: Array<\n  Promise<DynamicConfig | MinimalDynamicConfig>\n> = [];\n\n/**\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\n * fetch completes. If already populated, gtag config calls can use this to\n * selectively wait for only this app's initialization promise (FID) instead of all\n * initialization promises.\n */\nconst measurementIdToAppId: { [measurementId: string]: string } = {};\n\n/**\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\n */\nlet dataLayerName: string = 'dataLayer';\n\n/**\n * Name for window global gtag function used by GA: defaults to 'gtag'.\n */\nlet gtagName: string = 'gtag';\n\n/**\n * Reproduction of standard gtag function or reference to existing\n * gtag function on window object.\n */\nlet gtagCoreFunction: Gtag;\n\n/**\n * Wrapper around gtag function that ensures FID is sent with all\n * relevant event and config calls.\n */\nexport let wrappedGtagFunction: Gtag;\n\n/**\n * Flag to ensure page initialization steps (creation or wrapping of\n * dataLayer and gtag script) are only run once per page load.\n */\nlet globalInitDone: boolean = false;\n\n/**\n * For testing\n * @internal\n */\nexport function resetGlobalVars(\n  newGlobalInitDone = false,\n  newInitializationPromisesMap = {},\n  newDynamicPromises = []\n): void {\n  globalInitDone = newGlobalInitDone;\n  initializationPromisesMap = newInitializationPromisesMap;\n  dynamicConfigPromisesList = newDynamicPromises;\n  dataLayerName = 'dataLayer';\n  gtagName = 'gtag';\n}\n\n/**\n * For testing\n * @internal\n */\nexport function getGlobalVars(): {\n  initializationPromisesMap: { [appId: string]: Promise<string> };\n  dynamicConfigPromisesList: Array<\n    Promise<DynamicConfig | MinimalDynamicConfig>\n  >;\n} {\n  return {\n    initializationPromisesMap,\n    dynamicConfigPromisesList\n  };\n}\n\n/**\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\n * Intended to be used if `gtag.js` script has been installed on\n * this page independently of Firebase Analytics, and is using non-default\n * names for either the `gtag` function or for `dataLayer`.\n * Must be called before calling `getAnalytics()` or it won't\n * have any effect.\n *\n * @public\n *\n * @param options - Custom gtag and dataLayer names.\n */\nexport function settings(options: SettingsOptions): void {\n  if (globalInitDone) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n  }\n  if (options.dataLayerName) {\n    dataLayerName = options.dataLayerName;\n  }\n  if (options.gtagName) {\n    gtagName = options.gtagName;\n  }\n}\n\n/**\n * Returns true if no environment mismatch is found.\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\n * error that also lists details for each mismatch found.\n */\nfunction warnOnBrowserContextMismatch(): void {\n  const mismatchedEnvMessages = [];\n  if (isBrowserExtension()) {\n    mismatchedEnvMessages.push('This is a browser extension environment.');\n  }\n  if (!areCookiesEnabled()) {\n    mismatchedEnvMessages.push('Cookies are not available.');\n  }\n  if (mismatchedEnvMessages.length > 0) {\n    const details = mismatchedEnvMessages\n      .map((message, index) => `(${index + 1}) ${message}`)\n      .join(' ');\n    const err = ERROR_FACTORY.create(AnalyticsError.INVALID_ANALYTICS_CONTEXT, {\n      errorInfo: details\n    });\n    logger.warn(err.message);\n  }\n}\n\n/**\n * Analytics instance factory.\n * @internal\n */\nexport function factory(\n  app: FirebaseApp,\n  installations: _FirebaseInstallationsInternal,\n  options?: AnalyticsSettings\n): AnalyticsService {\n  warnOnBrowserContextMismatch();\n  const appId = app.options.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(AnalyticsError.NO_APP_ID);\n  }\n  if (!app.options.apiKey) {\n    if (app.options.measurementId) {\n      logger.warn(\n        `The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` +\n          ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` +\n          ` provided in the \"measurementId\" field in the local Firebase config.`\n      );\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.NO_API_KEY);\n    }\n  }\n  if (initializationPromisesMap[appId] != null) {\n    throw ERROR_FACTORY.create(AnalyticsError.ALREADY_EXISTS, {\n      id: appId\n    });\n  }\n\n  if (!globalInitDone) {\n    // Steps here should only be done once per page: creation or wrapping\n    // of dataLayer and global gtag function.\n\n    getOrCreateDataLayer(dataLayerName);\n\n    const { wrappedGtag, gtagCore } = wrapOrCreateGtag(\n      initializationPromisesMap,\n      dynamicConfigPromisesList,\n      measurementIdToAppId,\n      dataLayerName,\n      gtagName\n    );\n    wrappedGtagFunction = wrappedGtag;\n    gtagCoreFunction = gtagCore;\n\n    globalInitDone = true;\n  }\n  // Async but non-blocking.\n  // This map reflects the completion state of all promises for each appId.\n  initializationPromisesMap[appId] = _initializeAnalytics(\n    app,\n    dynamicConfigPromisesList,\n    measurementIdToAppId,\n    installations,\n    gtagCoreFunction,\n    dataLayerName,\n    options\n  );\n\n  const analyticsInstance: AnalyticsService = new AnalyticsService(app);\n\n  return analyticsInstance;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable camelcase */\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport {\n  Analytics,\n  AnalyticsCallOptions,\n  AnalyticsSettings,\n  ConsentSettings,\n  CustomParams,\n  EventNameString,\n  EventParams\n} from './public-types';\nimport { Provider } from '@firebase/component';\nimport {\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable,\n  areCookiesEnabled,\n  isBrowserExtension,\n  getModularInstance,\n  deepEqual\n} from '@firebase/util';\nimport { ANALYTICS_TYPE, GtagCommand } from './constants';\nimport {\n  AnalyticsService,\n  initializationPromisesMap,\n  wrappedGtagFunction\n} from './factory';\nimport { logger } from './logger';\nimport {\n  logEvent as internalLogEvent,\n  setCurrentScreen as internalSetCurrentScreen,\n  setUserId as internalSetUserId,\n  setUserProperties as internalSetUserProperties,\n  setAnalyticsCollectionEnabled as internalSetAnalyticsCollectionEnabled,\n  _setConsentDefaultForInit,\n  _setDefaultEventParametersForInit,\n  internalGetGoogleAnalyticsClientId\n} from './functions';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\n\nexport { settings } from './factory';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [ANALYTICS_TYPE]: AnalyticsService;\n  }\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function getAnalytics(app: FirebaseApp = getApp()): Analytics {\n  app = getModularInstance(app);\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n\n  if (analyticsProvider.isInitialized()) {\n    return analyticsProvider.getImmediate();\n  }\n\n  return initializeAnalytics(app);\n}\n\n/**\n * Returns an {@link Analytics} instance for the given app.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport function initializeAnalytics(\n  app: FirebaseApp,\n  options: AnalyticsSettings = {}\n): Analytics {\n  // Dependencies\n  const analyticsProvider: Provider<'analytics'> = _getProvider(\n    app,\n    ANALYTICS_TYPE\n  );\n  if (analyticsProvider.isInitialized()) {\n    const existingInstance = analyticsProvider.getImmediate();\n    if (deepEqual(options, analyticsProvider.getOptions())) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(AnalyticsError.ALREADY_INITIALIZED);\n    }\n  }\n  const analyticsInstance = analyticsProvider.initialize({ options });\n  return analyticsInstance;\n}\n\n/**\n * This is a public static method provided to users that wraps four different checks:\n *\n * 1. Check if it's not a browser extension environment.\n * 2. Check if cookies are enabled in current browser.\n * 3. Check if IndexedDB is supported by the browser environment.\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\n *\n * @public\n *\n */\nexport async function isSupported(): Promise<boolean> {\n  if (isBrowserExtension()) {\n    return false;\n  }\n  if (!areCookiesEnabled()) {\n    return false;\n  }\n  if (!isIndexedDBAvailable()) {\n    return false;\n  }\n\n  try {\n    const isDBOpenable: boolean = await validateIndexedDBOpenable();\n    return isDBOpenable;\n  } catch (error) {\n    return false;\n  }\n}\n\n/**\n * Use gtag `config` command to set `screen_name`.\n *\n * @public\n *\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param screenName - Screen name to set.\n */\nexport function setCurrentScreen(\n  analyticsInstance: Analytics,\n  screenName: string,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetCurrentScreen(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    screenName,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Retrieves a unique Google Analytics identifier for the web client.\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\n *\n * @public\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n */\nexport async function getGoogleAnalyticsClientId(\n  analyticsInstance: Analytics\n): Promise<string> {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  return internalGetGoogleAnalyticsClientId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!]\n  );\n}\n\n/**\n * Use gtag `config` command to set `user_id`.\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param id - User ID to set.\n */\nexport function setUserId(\n  analyticsInstance: Analytics,\n  id: string | null,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserId(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    id,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Use gtag `config` command to set all params specified.\n *\n * @public\n */\nexport function setUserProperties(\n  analyticsInstance: Analytics,\n  properties: CustomParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetUserProperties(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    properties,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Sets whether Google Analytics collection is enabled for this app on this device.\n * Sets global `window['ga-disable-analyticsId'] = true;`\n *\n * @public\n *\n * @param analyticsInstance - The {@link Analytics} instance.\n * @param enabled - If true, enables collection, if false, disables it.\n */\nexport function setAnalyticsCollectionEnabled(\n  analyticsInstance: Analytics,\n  enabled: boolean\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalSetAnalyticsCollectionEnabled(\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    enabled\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\n * all subsequent events.\n * @public\n * @param customParams - Any custom params the user may pass to gtag.js.\n */\nexport function setDefaultEventParameters(customParams: CustomParams): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.SET, customParams);\n  } else {\n    _setDefaultEventParametersForInit(customParams);\n  }\n}\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_payment_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    payment_type?: EventParams['payment_type'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_shipping_info',\n  eventParams?: {\n    coupon?: EventParams['coupon'];\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    shipping_tier?: EventParams['shipping_tier'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'add_to_cart' | 'add_to_wishlist' | 'remove_from_cart',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'begin_checkout',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'checkout_progress',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    coupon?: EventParams['coupon'];\n    value?: EventParams['value'];\n    items?: EventParams['items'];\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/exceptions\n * | Measure exceptions}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'exception',\n  eventParams?: {\n    description?: EventParams['description'];\n    fatal?: EventParams['fatal'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'generate_lead',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'login',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See\n * {@link https://developers.google.com/analytics/devguides/collection/ga4/views\n * | Page views}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'page_view',\n  eventParams?: {\n    page_title?: string;\n    page_location?: string;\n    page_path?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'purchase' | 'refund',\n  eventParams?: {\n    value?: EventParams['value'];\n    currency?: EventParams['currency'];\n    transaction_id: EventParams['transaction_id'];\n    tax?: EventParams['tax'];\n    shipping?: EventParams['shipping'];\n    items?: EventParams['items'];\n    coupon?: EventParams['coupon'];\n    affiliation?: EventParams['affiliation'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * See {@link https://firebase.google.com/docs/analytics/screenviews\n * | Track Screenviews}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'screen_view',\n  eventParams?: {\n    firebase_screen: EventParams['firebase_screen'];\n    firebase_screen_class: EventParams['firebase_screen_class'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'search' | 'view_search_results',\n  eventParams?: {\n    search_term?: EventParams['search_term'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_content',\n  eventParams?: {\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_item',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'select_promotion' | 'view_promotion',\n  eventParams?: {\n    items?: EventParams['items'];\n    promotion_id?: EventParams['promotion_id'];\n    promotion_name?: EventParams['promotion_name'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'set_checkout_option',\n  eventParams?: {\n    checkout_step?: EventParams['checkout_step'];\n    checkout_option?: EventParams['checkout_option'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'share',\n  eventParams?: {\n    method?: EventParams['method'];\n    content_type?: EventParams['content_type'];\n    item_id?: EventParams['item_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'sign_up',\n  eventParams?: {\n    method?: EventParams['method'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'timing_complete',\n  eventParams?: {\n    name: string;\n    value: number;\n    event_category?: string;\n    event_label?: string;\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_cart' | 'view_item',\n  eventParams?: {\n    currency?: EventParams['currency'];\n    items?: EventParams['items'];\n    value?: EventParams['value'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: 'view_item_list',\n  eventParams?: {\n    items?: EventParams['items'];\n    item_list_name?: EventParams['item_list_name'];\n    item_list_id?: EventParams['item_list_id'];\n    [key: string]: any;\n  },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * @public\n * List of recommended event parameters can be found in\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n */\nexport function logEvent<T extends string>(\n  analyticsInstance: Analytics,\n  eventName: CustomEventName<T>,\n  eventParams?: { [key: string]: any },\n  options?: AnalyticsCallOptions\n): void;\n\n/**\n * Sends a Google Analytics event with given `eventParams`. This method\n * automatically associates this logged event with this Firebase web\n * app instance on this device.\n * List of official event parameters can be found in the gtag.js\n * reference documentation:\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\n * | the GA4 reference documentation}.\n *\n * @public\n */\nexport function logEvent(\n  analyticsInstance: Analytics,\n  eventName: string,\n  eventParams?: EventParams,\n  options?: AnalyticsCallOptions\n): void {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  internalLogEvent(\n    wrappedGtagFunction,\n    initializationPromisesMap[analyticsInstance.app.options.appId!],\n    eventName,\n    eventParams,\n    options\n  ).catch(e => logger.error(e));\n}\n\n/**\n * Any custom event name string not in the standard list of recommended\n * event names.\n * @public\n */\nexport type CustomEventName<T> = T extends EventNameString ? never : T;\n\n/**\n * Sets the applicable end user consent state for this web app across all gtag references once\n * Firebase Analytics is initialized.\n *\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\n * types are set to \"granted\".\n * @public\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\n */\nexport function setConsent(consentSettings: ConsentSettings): void {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(GtagCommand.CONSENT, 'update', consentSettings);\n  } else {\n    _setConsentDefaultForInit(consentSettings);\n  }\n}\n", "/**\n * The Firebase Analytics Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { FirebaseAnalyticsInternal } from '@firebase/analytics-interop-types';\nimport { factory } from './factory';\nimport { ANALYTICS_TYPE } from './constants';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactoryOptions\n} from '@firebase/component';\nimport { ERROR_FACTORY, AnalyticsError } from './errors';\nimport { logEvent } from './api';\nimport { name, version } from '../package.json';\nimport { AnalyticsCallOptions } from './public-types';\nimport '@firebase/installations';\n\ndeclare global {\n  interface Window {\n    [key: string]: unknown;\n  }\n}\n\nfunction registerAnalytics(): void {\n  _registerComponent(\n    new Component(\n      ANALYTICS_TYPE,\n      (container, { options: analyticsOptions }: InstanceFactoryOptions) => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const installations = container\n          .getProvider('installations-internal')\n          .getImmediate();\n\n        return factory(app, installations, analyticsOptions);\n      },\n      ComponentType.PUBLIC\n    )\n  );\n\n  _registerComponent(\n    new Component('analytics-internal', internalFactory, ComponentType.PRIVATE)\n  );\n\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n\n  function internalFactory(\n    container: ComponentContainer\n  ): FirebaseAnalyticsInternal {\n    try {\n      const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\n      return {\n        logEvent: (\n          eventName: string,\n          eventParams?: { [key: string]: unknown },\n          options?: AnalyticsCallOptions\n        ) => logEvent(analytics, eventName, eventParams, options)\n      };\n    } catch (e) {\n      throw ERROR_FACTORY.create(AnalyticsError.INTEROP_COMPONENT_REG_FAILED, {\n        reason: e as Error\n      });\n    }\n  }\n}\n\nregisterAnalytics();\n\nexport * from './api';\nexport * from './public-types';\n"], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAEH;;AAEG;AACI,MAAMA,cAAc,GAAG,WAAW;AAEzC;AACO,MAAMC,UAAU,GAAG,aAAa;AAChC,MAAMC,UAAU,GAAG,QAAQ;AAE3B,MAAMC,oBAAoB,GAAG,EAAE,GAAG,IAAI;AAEtC,MAAMC,kBAAkB,GAC7B,4EAA4E;AAEvE,MAAMC,QAAQ,GAAG,0CAA0C;;AC/BlE;;;;;;;;;;;;;;;AAeG;AAII,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAAC,qBAAqB,CAAC;;ACnBvD;;;;;;;;;;;;;;;AAeG;AAmBH,MAAMC,MAAM,GAA6B;EACvC,wDACE,qDAAqD,GACrD,mBAAmB,GACnB,qEAAqE;EACvE,kEACE,iFAAiF,GACjF,gFAAgF,GAChF,8DAA8D,GAC9D,yDAAyD;EAC3D,oFACE,kDAAkD,GAClD,sEAAsE,GACtE,4BAA4B;EAC9B,oFACE,uEAAuE;EACzE,8EACE,2DAA2D,GAC3D,8DAA8D,GAC9D,8EAA8E;EAChF,sEACE,2DAA2D,GAC3D,8DAA8D,GAC9D,8EAA8E;EAChF,wDACE,2EAA2E,GAC3E,+FAA+F;EACjG,kEACE,iEAAiE;EACnE,gDACE,qGAAqG,GACrG,0BAA0B;EAC5B,8CACE,oGAAoG,GACpG,yBAAyB;EAC3B,oDAA+B,iCAAiC;EAChE,sEACE;CACH;AAeM,MAAMC,aAAa,GAAG,IAAIC,YAAY,CAC3C,WAAW,EACX,WAAW,EACXF,MAAM,CACP;;AC3FD;;;;;;;;;;;;;;;AAeG;AAgBH;;AAEG;AACG,SAAUG,+BAA+BA,CAACC,GAAW;EACzD,IAAI,CAACA,GAAG,CAACC,UAAU,CAACR,QAAQ,CAAC,EAAE;IAC7B,MAAMS,GAAG,GAAGL,aAAa,CAACM,MAAM,CAAuC;MACrEC,OAAO,EAAEJ;IACV,EAAC;IACFN,MAAM,CAACW,IAAI,CAACH,GAAG,CAACI,OAAO,CAAC;IACxB,OAAO,EAAE;;EAEX,OAAON,GAAG;AACZ;AAEA;;;;;AAKG;AACG,SAAUO,iBAAiBA,CAC/BC,QAA2B;EAE3B,OAAOC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAACG,GAAG,CAACC,OAAO,IAAIA,OAAO,CAACC,KAAK,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;AACpE;AAEA;;;;;;AAMG;AACa,SAAAC,wBAAwBA,CACtCC,UAAkB,EAClBC,aAAgD;;;EAIhD,IAAIC,kBAA0D;EAC9D,IAAIC,MAAM,CAACC,YAAY,EAAE;IACvBF,kBAAkB,GAAGC,MAAM,CAACC,YAAY,CAACC,YAAY,CACnDL,UAAU,EACVC,aAAa,CACd;;EAEH,OAAOC,kBAAkB;AAC3B;AAEA;;;AAGG;AACa,SAAAI,eAAeA,CAC7BC,aAAqB,EACrBC,aAAqB;EAErB,MAAMN,kBAAkB,GAAGH,wBAAwB,CACjD,wBAAwB,EACxB;IACEU,eAAe,EAAE1B;EAClB,EACF;EAED,MAAM2B,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;;;EAI/C,MAAMC,aAAa,GAAG,GAAGpC,QAAQ,MAAM8B,aAAa,OAAOC,aAAa,EAAE;EACzEE,MAAM,CAACI,GAAiC,GAAGZ,kBAAkB,GACzDA,kBAAwC,EAAEO,eAAe,CAACI,aAAa,CAAC,GACzEA,aAAa;EAEjBH,MAAM,CAACK,KAAK,GAAG,IAAI;EACnBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;AACnC;AAEA;;;AAGG;AACG,SAAUQ,oBAAoBA,CAACX,aAAqB;;EAExD,IAAIY,SAAS,GAAc,EAAE;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAAClB,MAAM,CAACI,aAAa,CAAC,CAAC,EAAE;IACxCY,SAAS,GAAGhB,MAAM,CAACI,aAAa,CAAc;GAC/C,MAAM;IACLJ,MAAM,CAACI,aAAa,CAAC,GAAGY,SAAS;;EAEnC,OAAOA,SAAS;AAClB;AAEA;;;;;;;;;AASG;AACH,eAAeG,YAAYA,CACzBC,QAAc,EACdC,yBAA+D,EAC/DC,yBAEC,EACDC,oBAAyD,EACzDlB,aAAqB,EACrBmB,UAAuD;;;EAIvD,MAAMC,kBAAkB,GAAGF,oBAAoB,CAAClB,aAAuB,CAAC;EACxE,IAAI;IACF,IAAIoB,kBAAkB,EAAE;MACtB,MAAMJ,yBAAyB,CAACI,kBAAkB,CAAC;KACpD,MAAM;;;;;MAKL,MAAMC,oBAAoB,GAAG,MAAMtC,iBAAiB,CAClDkC,yBAAyB,CAC1B;MACD,MAAMK,WAAW,GAAGD,oBAAoB,CAACE,IAAI,CAC3CC,MAAM,IAAIA,MAAM,CAACxB,aAAa,KAAKA,aAAa,CACjD;MACD,IAAIsB,WAAW,EAAE;QACf,MAAMN,yBAAyB,CAACM,WAAW,CAACG,KAAK,CAAC;;;GAGvD,CAAC,OAAOnC,CAAC,EAAE;IACVpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC;;EAEjByB,QAAQ,CAAqB,mCAAAf,aAAa,EAAEmB,UAAU,CAAC;AACzD;AAEA;;;;;;;;AAQG;AACH,eAAeQ,WAAWA,CACxBZ,QAAc,EACdC,yBAA+D,EAC/DC,yBAEC,EACDjB,aAAqB,EACrBmB,UAAuD;EAEvD,IAAI;IACF,IAAIS,+BAA+B,GAA2B,EAAE;;;IAIhE,IAAIT,UAAU,IAAIA,UAAU,CAAC,SAAS,CAAC,EAAE;MACvC,IAAIU,YAAY,GAAsBV,UAAU,CAAC,SAAS,CAAC;;MAE3D,IAAI,CAACP,KAAK,CAACC,OAAO,CAACgB,YAAY,CAAC,EAAE;QAChCA,YAAY,GAAG,CAACA,YAAY,CAAC;;;;MAI/B,MAAMR,oBAAoB,GAAG,MAAMtC,iBAAiB,CAClDkC,yBAAyB,CAC1B;MACD,KAAK,MAAMa,QAAQ,IAAID,YAAY,EAAE;;QAEnC,MAAMP,WAAW,GAAGD,oBAAoB,CAACE,IAAI,CAC3CC,MAAM,IAAIA,MAAM,CAACxB,aAAa,KAAK8B,QAAQ,CAC5C;QACD,MAAMC,qBAAqB,GACzBT,WAAW,IAAIN,yBAAyB,CAACM,WAAW,CAACG,KAAK,CAAC;QAC7D,IAAIM,qBAAqB,EAAE;UACzBH,+BAA+B,CAACI,IAAI,CAACD,qBAAqB,CAAC;SAC5D,MAAM;;;;UAILH,+BAA+B,GAAG,EAAE;UACpC;;;;;;;IAQN,IAAIA,+BAA+B,CAACK,MAAM,KAAK,CAAC,EAAE;;MAEhDL,+BAA+B,GAAGM,MAAM,CAACC,MAAM,CAC7CnB,yBAAyB,CAC1B;;;;IAKH,MAAM/B,OAAO,CAACC,GAAG,CAAC0C,+BAA+B,CAAC;;IAElDb,QAAQ,kCAAoBf,aAAa,EAAEmB,UAAU,IAAI,EAAE,CAAC;GAC7D,CAAC,OAAO7B,CAAC,EAAE;IACVpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC;;AAEnB;AAEA;;;;;;;;AAQG;AACH,SAAS8C,QAAQA,CACfrB,QAAc;AACd;;;AAGG;AACHC,yBAA+D;AAC/D;;;AAGG;AACHC,yBAEC;AACD;;;;AAIG;AACHC,oBAAyD;EAEzD;;;;;AAKG;EACH,eAAemB,WAAWA,CACxBC,OAAgE,EAChE,GAAGC,IAAe;IAElB,IAAI;;MAEF,IAAID,OAAO,KAAsB,iCAAE;QACjC,MAAM,CAACtC,aAAa,EAAEmB,UAAU,CAAC,GAAGoB,IAAI;;QAExC,MAAMZ,WAAW,CACfZ,QAAQ,EACRC,yBAAyB,EACzBC,yBAAyB,EACzBjB,aAAuB,EACvBmB,UAAqC,CACtC;OACF,MAAM,IAAImB,OAAO,KAAuB,mCAAE;QACzC,MAAM,CAACtC,aAAa,EAAEmB,UAAU,CAAC,GAAGoB,IAAI;;QAExC,MAAMzB,YAAY,CAChBC,QAAQ,EACRC,yBAAyB,EACzBC,yBAAyB,EACzBC,oBAAoB,EACpBlB,aAAuB,EACvBmB,UAAqC,CACtC;OACF,MAAM,IAAImB,OAAO,KAAwB,qCAAE;QAC1C,MAAM,CAACE,aAAa,EAAErB,UAAU,CAAC,GAAGoB,IAAI;;QAExCxB,QAAQ,CAEN,qCAAAyB,aAAa,EACbrB,UAA6B,CAC9B;OACF,MAAM,IAAImB,OAAO,KAAoB,6BAAE;QACtC,MAAM,CAACtC,aAAa,EAAEyC,SAAS,EAAEC,QAAQ,CAAC,GAAGH,IAAI;QACjDxB,QAAQ,8BAENf,aAAuB,EACvByC,SAAmB,EACnBC,QAAwC,CACzC;OACF,MAAM,IAAIJ,OAAO,KAAoB,6BAAE;QACtC,MAAM,CAACK,YAAY,CAAC,GAAGJ,IAAI;;QAE3BxB,QAAQ,8BAAkB4B,YAA4B,CAAC;OACxD,MAAM;QACL5B,QAAQ,CAACuB,OAAO,EAAE,GAAGC,IAAI,CAAC;;KAE7B,CAAC,OAAOjD,CAAC,EAAE;MACVpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC;;;EAGnB,OAAO+C,WAAmB;AAC5B;AAEA;;;;;;;;;;AAUG;AACG,SAAUO,gBAAgBA,CAC9B5B,yBAA+D,EAC/DC,yBAEC,EACDC,oBAAyD,EACzDnB,aAAqB,EACrB8C,gBAAwB;;EAMxB,IAAI9B,QAAQ,GAAS,SAAAA,CAAU,GAAG+B,KAAgB;;IAE/CnD,MAAM,CAACI,aAAa,CAAe,CAACiC,IAAI,CAACe,SAAS,CAAC;EACtD,CAAC;;EAGD,IACEpD,MAAM,CAACkD,gBAAgB,CAAC,IACxB,OAAOlD,MAAM,CAACkD,gBAAgB,CAAC,KAAK,UAAU,EAC9C;;IAEA9B,QAAQ,GAAGpB,MAAM,CAACkD,gBAAgB,CAAC;;EAGrClD,MAAM,CAACkD,gBAAgB,CAAC,GAAGT,QAAQ,CACjCrB,QAAQ,EACRC,yBAAyB,EACzBC,yBAAyB,EACzBC,oBAAoB,CACrB;EAED,OAAO;IACLH,QAAQ;IACRiC,WAAW,EAAErD,MAAM,CAACkD,gBAAgB;GACrC;AACH;AAEA;;;AAGG;AACG,SAAUI,oBAAoBA,CAClClD,aAAqB;EAErB,MAAMmD,UAAU,GAAGvD,MAAM,CAACQ,QAAQ,CAACgD,oBAAoB,CAAC,QAAQ,CAAC;EACjE,KAAK,MAAMC,GAAG,IAAIlB,MAAM,CAACC,MAAM,CAACe,UAAU,CAAC,EAAE;IAC3C,IACEE,GAAG,CAAC9C,GAAG,IACP8C,GAAG,CAAC9C,GAAG,CAAC+C,QAAQ,CAACpF,QAAQ,CAAC,IAC1BmF,GAAG,CAAC9C,GAAG,CAAC+C,QAAQ,CAACtD,aAAa,CAAC,EAC/B;MACA,OAAOqD,GAAG;;;EAGd,OAAO,IAAI;AACb;;ACrZA;;;;;;;;;;;;;;;AAeG;AAoBH;;;;;AAKG;AACI,MAAME,iBAAiB,GAAG,EAAE;AAEnC;;AAEG;AACH,MAAMC,oBAAoB,GAAG,IAAI;AAEjC;;AAEG;AACH,MAAMC,SAAS;EACbC,YACSC,gBAA0D,KAAE,EAC5DC,cAAA,GAAyBJ,oBAAoB;IAD7C,IAAgB,CAAAG,gBAAA,GAAhBA,gBAAgB;IAChB,IAAc,CAAAC,cAAA,GAAdA,cAAc;;EAGvBC,mBAAmBA,CAACnC,KAAa;IAC/B,OAAO,IAAI,CAACiC,gBAAgB,CAACjC,KAAK,CAAC;;EAGrCoC,mBAAmBA,CAACpC,KAAa,EAAEqC,QAA0B;IAC3D,IAAI,CAACJ,gBAAgB,CAACjC,KAAK,CAAC,GAAGqC,QAAQ;;EAGzCC,sBAAsBA,CAACtC,KAAa;IAClC,OAAO,IAAI,CAACiC,gBAAgB,CAACjC,KAAK,CAAC;;AAEtC;AAED,MAAMuC,gBAAgB,GAAG,IAAIR,SAAS,EAAE;AAExC;;;AAGG;AACH,SAASS,UAAUA,CAACC,MAAc;EAChC,OAAO,IAAIC,OAAO,CAAC;IACjBC,MAAM,EAAE,kBAAkB;IAC1B,gBAAgB,EAAEF;EACnB,EAAC;AACJ;AAEA;;;AAGG;AACI,eAAeG,kBAAkBA,CACtCC,SAAoB;EAEpB,MAAM;IAAE7C,KAAK;IAAEyC;EAAM,CAAE,GAAGI,SAAS;EACnC,MAAMC,OAAO,GAAgB;IAC3BC,MAAM,EAAE,KAAK;IACbC,OAAO,EAAER,UAAU,CAACC,MAAM;GAC3B;EACD,MAAMQ,MAAM,GAAG1G,kBAAkB,CAAC2G,OAAO,CAAC,UAAU,EAAElD,KAAK,CAAC;EAC5D,MAAMmD,QAAQ,GAAG,MAAMC,KAAK,CAACH,MAAM,EAAEH,OAAO,CAAC;EAC7C,IAAIK,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;IACtD,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAI;;MAEF,MAAMC,YAAY,GAAI,MAAMJ,QAAQ,CAACK,IAAI,EAExC;MACD,IAAID,YAAY,CAACtD,KAAK,EAAE5C,OAAO,EAAE;QAC/BiG,YAAY,GAAGC,YAAY,CAACtD,KAAK,CAAC5C,OAAO;;KAE5C,CAAC,OAAOoG,QAAQ,EAAE;IACnB,MAAM7G,aAAa,CAACM,MAAM,CAAqC;MAC7DwG,UAAU,EAAEP,QAAQ,CAACE,MAAM;MAC3BM,eAAe,EAAEL;IAClB,EAAC;;EAEJ,OAAOH,QAAQ,CAACK,IAAI,EAAE;AACxB;AAEA;;;AAGG;AACI,eAAeI,2BAA2BA,CAC/CC,GAAgB;AAChB;AACAC,SAAuB,GAAAvB,gBAAgB,EACvCwB,aAAsB;EAEtB,MAAM;IAAE/D,KAAK;IAAEyC,MAAM;IAAElE;EAAa,CAAE,GAAGsF,GAAG,CAACG,OAAO;EAEpD,IAAI,CAAChE,KAAK,EAAE;IACV,MAAMpD,aAAa,CAACM,MAAM,4CAA0B;;EAGtD,IAAI,CAACuF,MAAM,EAAE;IACX,IAAIlE,aAAa,EAAE;MACjB,OAAO;QACLA,aAAa;QACbyB;OACD;;IAEH,MAAMpD,aAAa,CAACM,MAAM,8CAA2B;;EAGvD,MAAM+E,gBAAgB,GAAqB6B,SAAS,CAAC3B,mBAAmB,CACtEnC,KAAK,CACN,IAAI;IACHiE,YAAY,EAAE,CAAC;IACfC,qBAAqB,EAAEC,IAAI,CAACC,GAAG;GAChC;EAED,MAAMC,MAAM,GAAG,IAAIC,oBAAoB,EAAE;EAEzCC,UAAU,CACR,YAAW;;IAETF,MAAM,CAACG,KAAK,EAAE;EAChB,CAAC,EACDT,aAAa,KAAKU,SAAS,GAAGV,aAAa,GAAGzH,oBAAoB,CACnE;EAED,OAAOoI,kCAAkC,CACvC;IAAE1E,KAAK;IAAEyC,MAAM;IAAElE;EAAa,CAAE,EAChC0D,gBAAgB,EAChBoC,MAAM,EACNP,SAAS,CACV;AACH;AAEA;;;;;AAKG;AACH,eAAeY,kCAAkCA,CAC/C7B,SAAoB,EACpB;EAAEqB,qBAAqB;EAAED;AAAY,CAAoB,EACzDI,MAA4B,EAC5BP,SAAuB,GAAAvB,gBAAgB;AAAA,E;EAEvC,MAAM;IAAEvC,KAAK;IAAEzB;EAAa,CAAE,GAAGsE,SAAS;;;;EAI1C,IAAI;IACF,MAAM8B,mBAAmB,CAACN,MAAM,EAAEH,qBAAqB,CAAC;GACzD,CAAC,OAAOrG,CAAC,EAAE;IACV,IAAIU,aAAa,EAAE;MACjB9B,MAAM,CAACW,IAAI,CACT,wEAAwE,GACtE,uCAAuCmB,aAAa,EAAE,GACtD,yEACGV,CAAW,EAAER,OAChB,GAAG,CACN;MACD,OAAO;QAAE2C,KAAK;QAAEzB;MAAa,CAAE;;IAEjC,MAAMV,CAAC;;EAGT,IAAI;IACF,MAAMsF,QAAQ,GAAG,MAAMP,kBAAkB,CAACC,SAAS,CAAC;;IAGpDiB,SAAS,CAACxB,sBAAsB,CAACtC,KAAK,CAAC;IAEvC,OAAOmD,QAAQ;GAChB,CAAC,OAAOtF,CAAC,EAAE;IACV,MAAMoC,KAAK,GAAGpC,CAAU;IACxB,IAAI,CAAC+G,gBAAgB,CAAC3E,KAAK,CAAC,EAAE;MAC5B6D,SAAS,CAACxB,sBAAsB,CAACtC,KAAK,CAAC;MACvC,IAAIzB,aAAa,EAAE;QACjB9B,MAAM,CAACW,IAAI,CACT,qEAAqE,GACnE,uCAAuCmB,aAAa,EAAE,GACtD,yEAAyE0B,KAAK,EAAE5C,OAAO,GAAG,CAC7F;QACD,OAAO;UAAE2C,KAAK;UAAEzB;QAAa,CAAE;OAChC,MAAM;QACL,MAAMV,CAAC;;;IAIX,MAAMgH,aAAa,GACjBC,MAAM,CAAC7E,KAAK,EAAE8E,UAAU,EAAErB,UAAU,CAAC,KAAK,GAAG,GACzCsB,sBAAsB,CACpBf,YAAY,EACZH,SAAS,CAAC5B,cAAc,EACxBL,iBAAiB,CAClB,GACDmD,sBAAsB,CAACf,YAAY,EAAEH,SAAS,CAAC5B,cAAc,CAAC;;IAGpE,MAAMD,gBAAgB,GAAG;MACvBiC,qBAAqB,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAGS,aAAa;MACjDZ,YAAY,EAAEA,YAAY,GAAG;KAC9B;;IAGDH,SAAS,CAAC1B,mBAAmB,CAACpC,KAAK,EAAEiC,gBAAgB,CAAC;IACtDxF,MAAM,CAACwI,KAAK,CAAC,iCAAiCJ,aAAa,SAAS,CAAC;IAErE,OAAOH,kCAAkC,CACvC7B,SAAS,EACTZ,gBAAgB,EAChBoC,MAAM,EACNP,SAAS,CACV;;AAEL;AAEA;;;;;;;;;;;AAWG;AACH,SAASa,mBAAmBA,CAC1BN,MAA4B,EAC5BH,qBAA6B;EAE7B,OAAO,IAAI1G,OAAO,CAAC,CAAC0H,OAAO,EAAEC,MAAM,KAAI;;IAErC,MAAMN,aAAa,GAAGO,IAAI,CAACC,GAAG,CAACnB,qBAAqB,GAAGC,IAAI,CAACC,GAAG,EAAE,EAAE,CAAC,CAAC;IAErE,MAAMkB,OAAO,GAAGf,UAAU,CAACW,OAAO,EAAEL,aAAa,CAAC;;IAGlDR,MAAM,CAACkB,gBAAgB,CAAC,MAAK;MAC3BC,YAAY,CAACF,OAAO,CAAC;;MAErBH,MAAM,CACJvI,aAAa,CAACM,MAAM,CAAgC;QAClDgH;MACD,EAAC,CACH;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAIA;;AAEG;AACH,SAASU,gBAAgBA,CAAC/G,CAAQ;EAChC,IAAI,EAAEA,CAAC,YAAY4H,aAAa,CAAC,IAAI,CAAC5H,CAAC,CAACkH,UAAU,EAAE;IAClD,OAAO,KAAK;;;EAId,MAAMrB,UAAU,GAAGoB,MAAM,CAACjH,CAAC,CAACkH,UAAU,CAAC,YAAY,CAAC,CAAC;EAErD,OACErB,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,GAAG;AAEtB;AAEA;;;;;;;AAOG;MACUY,oBAAoB;EAAjCtC,YAAA;IACE,IAAS,CAAA0D,SAAA,GAAsB,EAAE;;EACjCH,gBAAgBA,CAACI,QAAoB;IACnC,IAAI,CAACD,SAAS,CAACnF,IAAI,CAACoF,QAAQ,CAAC;;EAE/BnB,KAAKA,CAAA;IACH,IAAI,CAACkB,SAAS,CAACE,OAAO,CAACD,QAAQ,IAAIA,QAAQ,EAAE,CAAC;;AAEjD;;AClUD;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACI,IAAIE,6BAAuD;AAElE;;;;;;AAMG;AACI,eAAeC,UAAQC,CAC5BC,YAAkB,EAClB1F,qBAAsC,EACtC2F,SAAiB,EACjBC,WAAyB,EACzBlC,OAA8B;EAE9B,IAAIA,OAAO,IAAIA,OAAO,CAACmC,MAAM,EAAE;IAC7BH,YAAY,CAAoB,iCAAAC,SAAS,EAAEC,WAAW,CAAC;IACvD;GACD,MAAM;IACL,MAAM3H,aAAa,GAAG,MAAM+B,qBAAqB;IACjD,MAAM8F,MAAM,GAAgC;MAC1C,GAAGF,WAAW;MACd,SAAS,EAAE3H;KACZ;IACDyH,YAAY,CAAoB,iCAAAC,SAAS,EAAEG,MAAM,CAAC;;AAEtD;AAEA;;;;;;;;AAQG;AACI,eAAeC,kBAAgBC,CACpCN,YAAkB,EAClB1F,qBAAsC,EACtCiG,UAAyB,EACzBvC,OAA8B;EAE9B,IAAIA,OAAO,IAAIA,OAAO,CAACmC,MAAM,EAAE;IAC7BH,YAAY,8BAAkB;MAAE,aAAa,EAAEO;IAAU,CAAE,CAAC;IAC5D,OAAO/I,OAAO,CAAC0H,OAAO,EAAE;GACzB,MAAM;IACL,MAAM3G,aAAa,GAAG,MAAM+B,qBAAqB;IACjD0F,YAAY,oCAAqBzH,aAAa,EAAE;MAC9CiI,MAAM,EAAE,IAAI;MACZ,aAAa,EAAED;IAChB,EAAC;;AAEN;AAEA;;;;;AAKG;AACI,eAAeE,WAASC,CAC7BV,YAAkB,EAClB1F,qBAAsC,EACtCqG,EAAiB,EACjB3C,OAA8B;EAE9B,IAAIA,OAAO,IAAIA,OAAO,CAACmC,MAAM,EAAE;IAC7BH,YAAY,8BAAkB;MAAE,SAAS,EAAEW;IAAE,CAAE,CAAC;IAChD,OAAOnJ,OAAO,CAAC0H,OAAO,EAAE;GACzB,MAAM;IACL,MAAM3G,aAAa,GAAG,MAAM+B,qBAAqB;IACjD0F,YAAY,oCAAqBzH,aAAa,EAAE;MAC9CiI,MAAM,EAAE,IAAI;MACZ,SAAS,EAAEG;IACZ,EAAC;;AAEN;AAEA;;;;;AAKG;AACI,eAAeC,mBAAiBC,CACrCb,YAAkB,EAClB1F,qBAAsC,EACtCwG,UAAwB,EACxB9C,OAA8B;EAE9B,IAAIA,OAAO,IAAIA,OAAO,CAACmC,MAAM,EAAE;IAC7B,MAAMY,cAAc,GAA+B,EAAE;IACrD,KAAK,MAAMC,GAAG,IAAIvG,MAAM,CAACwG,IAAI,CAACH,UAAU,CAAC,EAAE;;MAEzCC,cAAc,CAAC,mBAAmBC,GAAG,EAAE,CAAC,GAAGF,UAAU,CAACE,GAAG,CAAC;;IAE5DhB,YAAY,8BAAkBe,cAAc,CAAC;IAC7C,OAAOvJ,OAAO,CAAC0H,OAAO,EAAE;GACzB,MAAM;IACL,MAAM3G,aAAa,GAAG,MAAM+B,qBAAqB;IACjD0F,YAAY,oCAAqBzH,aAAa,EAAE;MAC9CiI,MAAM,EAAE,IAAI;MACZ,iBAAiB,EAAEM;IACpB,EAAC;;AAEN;AAEA;;;;;AAKG;AACI,eAAeI,kCAAkCA,CACtDlB,YAAkB,EAClB1F,qBAAsC;EAEtC,MAAM/B,aAAa,GAAG,MAAM+B,qBAAqB;EACjD,OAAO,IAAI9C,OAAO,CAAC,CAAC0H,OAAO,EAAEC,MAAM,KAAI;IACrCa,YAAY,8BAEVzH,aAAa,EACb,WAAW,EACV4I,QAAgB,IAAI;MACnB,IAAI,CAACA,QAAQ,EAAE;QACbhC,MAAM,CAACvI,aAAa,CAACM,MAAM,kDAA6B,CAAC;;MAE3DgI,OAAO,CAACiC,QAAQ,CAAC;IACnB,CAAC,CACF;EACH,CAAC,CAAC;AACJ;AAEA;;;;AAIG;AACI,eAAeC,+BAA6BC,CACjD/G,qBAAsC,EACtCgH,OAAgB;EAEhB,MAAM/I,aAAa,GAAG,MAAM+B,qBAAqB;EACjDpC,MAAM,CAAC,cAAcK,aAAa,EAAE,CAAC,GAAG,CAAC+I,OAAO;AAClD;AAEA;;AAEG;AACI,IAAIC,6BAA0D;AAErE;;;;;AAKG;AACG,SAAUC,yBAAyBA,CACvCC,eAAiC;EAEjCF,6BAA6B,GAAGE,eAAe;AACjD;AAEA;;;;;AAKG;AACG,SAAUC,iCAAiCA,CAC/CxG,YAA2B;EAE3B2E,6BAA6B,GAAG3E,YAAY;AAC9C;;AC9MA;;;;;;;;;;;;;;;AAeG;AAsBH,eAAeyG,iBAAiBA,CAAA;EAC9B,IAAI,CAACC,oBAAoB,EAAE,EAAE;IAC3BnL,MAAM,CAACW,IAAI,CACTR,aAAa,CAACM,MAAM,CAAuC;MACzD2K,SAAS,EAAE;KACZ,CAAC,CAACxK,OAAO,CACX;IACD,OAAO,KAAK;GACb,MAAM;IACL,IAAI;MACF,MAAMyK,yBAAyB,EAAE;KAClC,CAAC,OAAOjK,CAAC,EAAE;MACVpB,MAAM,CAACW,IAAI,CACTR,aAAa,CAACM,MAAM,CAAuC;QACzD2K,SAAS,EAAGhK,CAAW,EAAEkK,QAAQ;OAClC,CAAC,CAAC1K,OAAO,CACX;MACD,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEA;;;;;;;;;;;;AAYG;AACI,eAAe2K,oBAAoBA,CACxCnE,GAAgB,EAChBrE,yBAEC,EACDC,oBAA+C,EAC/CwI,aAA6C,EAC7C3I,QAAc,EACdhB,aAAqB,EACrB0F,OAA2B;EAE3B,MAAMkE,oBAAoB,GAAGtE,2BAA2B,CAACC,GAAG,CAAC;;EAE7DqE,oBAAoB,CACjBC,IAAI,CAACpI,MAAM,IAAG;IACbN,oBAAoB,CAACM,MAAM,CAACxB,aAAa,CAAC,GAAGwB,MAAM,CAACC,KAAK;IACzD,IACE6D,GAAG,CAACG,OAAO,CAACzF,aAAa,IACzBwB,MAAM,CAACxB,aAAa,KAAKsF,GAAG,CAACG,OAAO,CAACzF,aAAa,EAClD;MACA9B,MAAM,CAACW,IAAI,CACT,oDAAoDyG,GAAG,CAACG,OAAO,CAACzF,aAAa,GAAG,GAC9E,+DAA+DwB,MAAM,CAACxB,aAAa,IAAI,GACvF,gFAAgF,GAChF,aAAa,GACb,+EAA+E,CAClF;;EAEL,CAAC,CAAC,CACDX,KAAK,CAACC,CAAC,IAAIpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC,CAAC;;EAE9B2B,yBAAyB,CAACe,IAAI,CAAC2H,oBAAoB,CAAC;EAEpD,MAAME,UAAU,GAAgCT,iBAAiB,EAAE,CAACQ,IAAI,CACtEE,UAAU,IAAG;IACX,IAAIA,UAAU,EAAE;MACd,OAAOJ,aAAa,CAACK,KAAK,EAAE;KAC7B,MAAM;MACL,OAAO7D,SAAS;;EAEpB,CAAC,CACF;EAED,MAAM,CAAC8D,aAAa,EAAEC,GAAG,CAAC,GAAG,MAAMhL,OAAO,CAACC,GAAG,CAAC,CAC7CyK,oBAAoB,EACpBE,UAAU,CACX,CAAC;;;EAIF,IAAI,CAAC5G,oBAAoB,CAAClD,aAAa,CAAC,EAAE;IACxCD,eAAe,CAACC,aAAa,EAAEiK,aAAa,CAAChK,aAAa,CAAC;;;EAI7D,IAAIgJ,6BAA6B,EAAE;IACjCjI,QAAQ,CAAsB,8CAAS,EAAEiI,6BAA6B,CAAC;IACvEC,yBAAyB,CAAC/C,SAAS,CAAC;;;;;;EAOrCnF,QAAgB,CAAC,IAAI,EAAE,IAAI6E,IAAI,EAAE,CAAC;;;EAGnC,MAAMsE,gBAAgB,GAA4BzE,OAAO,EAAEjE,MAAM,IAAI,EAAE;;EAGvE0I,gBAAgB,CAACpM,UAAU,CAAC,GAAG,UAAU;EACzCoM,gBAAgB,CAACjC,MAAM,GAAG,IAAI;EAE9B,IAAIgC,GAAG,IAAI,IAAI,EAAE;IACfC,gBAAgB,CAACrM,UAAU,CAAC,GAAGoM,GAAG;;;;;;EAOpClJ,QAAQ,oCAAqBiJ,aAAa,CAAChK,aAAa,EAAEkK,gBAAgB,CAAC;;EAG3E,IAAI5C,6BAA6B,EAAE;IACjCvG,QAAQ,8BAAkBuG,6BAA6B,CAAC;IACxD6B,iCAAiC,CAACjD,SAAS,CAAC;;EAG9C,OAAO8D,aAAa,CAAChK,aAAa;AACpC;;ACnKA;;;;;;;;;;;;;;;AAeG;AAYH;;AAEG;MACUmK,gBAAgB;EAC3B1G,YAAmB6B,GAAgB;IAAhB,IAAG,CAAAA,GAAA,GAAHA,GAAG;;EACtB8E,OAAOA,CAAA;IACL,OAAOpJ,yBAAyB,CAAC,IAAI,CAACsE,GAAG,CAACG,OAAO,CAAChE,KAAM,CAAC;IACzD,OAAOxC,OAAO,CAAC0H,OAAO,EAAE;;AAE3B;AAED;;;;AAIG;AACI,IAAI3F,yBAAyB,GAEhC,EAAE;AAEN;;;;AAIG;AACH,IAAIC,yBAAyB,GAEzB,EAAE;AAEN;;;;;AAKG;AACH,MAAMC,oBAAoB,GAAwC,EAAE;AAEpE;;AAEG;AACH,IAAInB,aAAa,GAAW,WAAW;AAEvC;;AAEG;AACH,IAAIsK,QAAQ,GAAW,MAAM;AAE7B;;;AAGG;AACH,IAAIC,gBAAsB;AAE1B;;;AAGG;AACI,IAAIC,mBAAyB;AAEpC;;;AAGG;AACH,IAAIC,cAAc,GAAY,KAAK;AAkCnC;;;;;;;;;;;AAWG;AACG,SAAUC,QAAQA,CAAChF,OAAwB;EAC/C,IAAI+E,cAAc,EAAE;IAClB,MAAMnM,aAAa,CAACM,MAAM,gEAAoC;;EAEhE,IAAI8G,OAAO,CAAC1F,aAAa,EAAE;IACzBA,aAAa,GAAG0F,OAAO,CAAC1F,aAAa;;EAEvC,IAAI0F,OAAO,CAAC4E,QAAQ,EAAE;IACpBA,QAAQ,GAAG5E,OAAO,CAAC4E,QAAQ;;AAE/B;AAEA;;;;AAIG;AACH,SAASK,4BAA4BA,CAAA;EACnC,MAAMC,qBAAqB,GAAG,EAAE;EAChC,IAAIC,kBAAkB,EAAE,EAAE;IACxBD,qBAAqB,CAAC3I,IAAI,CAAC,0CAA0C,CAAC;;EAExE,IAAI,CAAC6I,iBAAiB,EAAE,EAAE;IACxBF,qBAAqB,CAAC3I,IAAI,CAAC,4BAA4B,CAAC;;EAE1D,IAAI2I,qBAAqB,CAAC1I,MAAM,GAAG,CAAC,EAAE;IACpC,MAAM6I,OAAO,GAAGH,qBAAqB,CAClCxL,GAAG,CAAC,CAACL,OAAO,EAAEiM,KAAK,KAAK,IAAIA,KAAK,GAAG,CAAC,KAAKjM,OAAO,EAAE,CAAC,CACpDkM,IAAI,CAAC,GAAG,CAAC;IACZ,MAAMtM,GAAG,GAAGL,aAAa,CAACM,MAAM,CAA2C;MACzE2K,SAAS,EAAEwB;IACZ,EAAC;IACF5M,MAAM,CAACW,IAAI,CAACH,GAAG,CAACI,OAAO,CAAC;;AAE5B;AAEA;;;AAGG;SACamM,OAAOA,CACrB3F,GAAgB,EAChBoE,aAA6C,EAC7CjE,OAA2B;EAE3BiF,4BAA4B,EAAE;EAC9B,MAAMjJ,KAAK,GAAG6D,GAAG,CAACG,OAAO,CAAChE,KAAK;EAC/B,IAAI,CAACA,KAAK,EAAE;IACV,MAAMpD,aAAa,CAACM,MAAM,4CAA0B;;EAEtD,IAAI,CAAC2G,GAAG,CAACG,OAAO,CAACvB,MAAM,EAAE;IACvB,IAAIoB,GAAG,CAACG,OAAO,CAACzF,aAAa,EAAE;MAC7B9B,MAAM,CAACW,IAAI,CACT,8FAA8F,GAC5F,6EAA6EyG,GAAG,CAACG,OAAO,CAACzF,aAAa,EAAE,GACxG,sEAAsE,CACzE;KACF,MAAM;MACL,MAAM3B,aAAa,CAACM,MAAM,8CAA2B;;;EAGzD,IAAIqC,yBAAyB,CAACS,KAAK,CAAC,IAAI,IAAI,EAAE;IAC5C,MAAMpD,aAAa,CAACM,MAAM,CAAgC;MACxDyJ,EAAE,EAAE3G;IACL,EAAC;;EAGJ,IAAI,CAAC+I,cAAc,EAAE;;;IAInB9J,oBAAoB,CAACX,aAAa,CAAC;IAEnC,MAAM;MAAEiD,WAAW;MAAEjC;IAAQ,CAAE,GAAG6B,gBAAgB,CAChD5B,yBAAyB,EACzBC,yBAAyB,EACzBC,oBAAoB,EACpBnB,aAAa,EACbsK,QAAQ,CACT;IACDE,mBAAmB,GAAGvH,WAAW;IACjCsH,gBAAgB,GAAGvJ,QAAQ;IAE3ByJ,cAAc,GAAG,IAAI;;;;EAIvBxJ,yBAAyB,CAACS,KAAK,CAAC,GAAGgI,oBAAoB,CACrDnE,GAAG,EACHrE,yBAAyB,EACzBC,oBAAoB,EACpBwI,aAAa,EACbY,gBAAgB,EAChBvK,aAAa,EACb0F,OAAO,CACR;EAED,MAAMyF,iBAAiB,GAAqB,IAAIf,gBAAgB,CAAC7E,GAAG,CAAC;EAErE,OAAO4F,iBAAiB;AAC1B;;AC5OA;AAiEA;;;;;;AAMG;AACa,SAAAC,YAAYA,CAAC7F,GAAA,GAAmB8F,MAAM,EAAE;EACtD9F,GAAG,GAAG+F,kBAAkB,CAAC/F,GAAG,CAAC;;EAE7B,MAAMgG,iBAAiB,GAA0BC,YAAY,CAC3DjG,GAAG,EACH1H,cAAc,CACf;EAED,IAAI0N,iBAAiB,CAACE,aAAa,EAAE,EAAE;IACrC,OAAOF,iBAAiB,CAACG,YAAY,EAAE;;EAGzC,OAAOC,mBAAmB,CAACpG,GAAG,CAAC;AACjC;AAEA;;;;;;AAMG;SACaoG,mBAAmBA,CACjCpG,GAAgB,EAChBG,OAAA,GAA6B,EAAE;;EAG/B,MAAM6F,iBAAiB,GAA0BC,YAAY,CAC3DjG,GAAG,EACH1H,cAAc,CACf;EACD,IAAI0N,iBAAiB,CAACE,aAAa,EAAE,EAAE;IACrC,MAAMG,gBAAgB,GAAGL,iBAAiB,CAACG,YAAY,EAAE;IACzD,IAAIG,SAAS,CAACnG,OAAO,EAAE6F,iBAAiB,CAACO,UAAU,EAAE,CAAC,EAAE;MACtD,OAAOF,gBAAgB;KACxB,MAAM;MACL,MAAMtN,aAAa,CAACM,MAAM,gEAAoC;;;EAGlE,MAAMuM,iBAAiB,GAAGI,iBAAiB,CAACQ,UAAU,CAAC;IAAErG;EAAO,CAAE,CAAC;EACnE,OAAOyF,iBAAiB;AAC1B;AAEA;;;;;;;;;;AAUG;AACI,eAAea,WAAWA,CAAA;EAC/B,IAAInB,kBAAkB,EAAE,EAAE;IACxB,OAAO,KAAK;;EAEd,IAAI,CAACC,iBAAiB,EAAE,EAAE;IACxB,OAAO,KAAK;;EAEd,IAAI,CAACxB,oBAAoB,EAAE,EAAE;IAC3B,OAAO,KAAK;;EAGd,IAAI;IACF,MAAM2C,YAAY,GAAY,MAAMzC,yBAAyB,EAAE;IAC/D,OAAOyC,YAAY;GACpB,CAAC,OAAOtK,KAAK,EAAE;IACd,OAAO,KAAK;;AAEhB;AAEA;;;;;;;;;;AAUG;SACaqG,gBAAgBA,CAC9BmD,iBAA4B,EAC5BlD,UAAkB,EAClBvC,OAA8B;EAE9ByF,iBAAiB,GAAGG,kBAAkB,CAACH,iBAAiB,CAAC;EACzDpD,kBAAwB,CACtByC,mBAAmB,EACnBvJ,yBAAyB,CAACkK,iBAAiB,CAAC5F,GAAG,CAACG,OAAO,CAAChE,KAAM,CAAC,EAC/DuG,UAAU,EACVvC,OAAO,CACR,CAACpG,KAAK,CAACC,CAAC,IAAIpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC,CAAC;AAC/B;AAEA;;;;;;;AAOG;AACI,eAAe2M,0BAA0BA,CAC9Cf,iBAA4B;EAE5BA,iBAAiB,GAAGG,kBAAkB,CAACH,iBAAiB,CAAC;EACzD,OAAOvC,kCAAkC,CACvC4B,mBAAmB,EACnBvJ,yBAAyB,CAACkK,iBAAiB,CAAC5F,GAAG,CAACG,OAAO,CAAChE,KAAM,CAAC,CAChE;AACH;AAEA;;;;;;;AAOG;SACa0G,SAASA,CACvB+C,iBAA4B,EAC5B9C,EAAiB,EACjB3C,OAA8B;EAE9ByF,iBAAiB,GAAGG,kBAAkB,CAACH,iBAAiB,CAAC;EACzDhD,WAAiB,CACfqC,mBAAmB,EACnBvJ,yBAAyB,CAACkK,iBAAiB,CAAC5F,GAAG,CAACG,OAAO,CAAChE,KAAM,CAAC,EAC/D2G,EAAE,EACF3C,OAAO,CACR,CAACpG,KAAK,CAACC,CAAC,IAAIpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC,CAAC;AAC/B;AAEA;;;;AAIG;SACagJ,iBAAiBA,CAC/B4C,iBAA4B,EAC5B3C,UAAwB,EACxB9C,OAA8B;EAE9ByF,iBAAiB,GAAGG,kBAAkB,CAACH,iBAAiB,CAAC;EACzD7C,mBAAyB,CACvBkC,mBAAmB,EACnBvJ,yBAAyB,CAACkK,iBAAiB,CAAC5F,GAAG,CAACG,OAAO,CAAChE,KAAM,CAAC,EAC/D8G,UAAU,EACV9C,OAAO,CACR,CAACpG,KAAK,CAACC,CAAC,IAAIpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC,CAAC;AAC/B;AAEA;;;;;;;;AAQG;AACa,SAAAwJ,6BAA6BA,CAC3CoC,iBAA4B,EAC5BnC,OAAgB;EAEhBmC,iBAAiB,GAAGG,kBAAkB,CAACH,iBAAiB,CAAC;EACzDrC,+BAAqC,CACnC7H,yBAAyB,CAACkK,iBAAiB,CAAC5F,GAAG,CAACG,OAAO,CAAChE,KAAM,CAAC,EAC/DsH,OAAO,CACR,CAAC1J,KAAK,CAACC,CAAC,IAAIpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC,CAAC;AAC/B;AAEA;;;;;;AAMG;AACG,SAAU4M,yBAAyBA,CAACvJ,YAA0B;;EAElE,IAAI4H,mBAAmB,EAAE;IACvBA,mBAAmB,8BAAkB5H,YAAY,CAAC;GACnD,MAAM;IACLwG,iCAAiC,CAACxG,YAAY,CAAC;;AAEnD;AA6cA;;;;;;;;;;AAUG;AACG,SAAU6E,QAAQA,CACtB0D,iBAA4B,EAC5BxD,SAAiB,EACjBC,WAAyB,EACzBlC,OAA8B;EAE9ByF,iBAAiB,GAAGG,kBAAkB,CAACH,iBAAiB,CAAC;EACzD3D,UAAgB,CACdgD,mBAAmB,EACnBvJ,yBAAyB,CAACkK,iBAAiB,CAAC5F,GAAG,CAACG,OAAO,CAAChE,KAAM,CAAC,EAC/DiG,SAAS,EACTC,WAAW,EACXlC,OAAO,CACR,CAACpG,KAAK,CAACC,CAAC,IAAIpB,MAAM,CAACwD,KAAK,CAACpC,CAAC,CAAC,CAAC;AAC/B;AASA;;;;;;;;AAQG;AACG,SAAU6M,UAAUA,CAACjD,eAAgC;;EAEzD,IAAIqB,mBAAmB,EAAE;IACvBA,mBAAmB,CAAsB,6CAAQ,EAAErB,eAAe,CAAC;GACpE,MAAM;IACLD,yBAAyB,CAACC,eAAe,CAAC;;AAE9C;;;;ACtwBA;;;;;AAKG;AAyCH,SAASkD,iBAAiBA,CAAA;EACxBC,kBAAkB,CAChB,IAAIC,SAAS,CACX1O,cAAc,EACd,CAAC2O,SAAS,EAAE;IAAE9G,OAAO,EAAE+G;EAAgB,CAA0B,KAAI;;IAEnE,MAAMlH,GAAG,GAAGiH,SAAS,CAACE,WAAW,CAAC,KAAK,CAAC,CAAChB,YAAY,EAAE;IACvD,MAAM/B,aAAa,GAAG6C,SAAS,CAC5BE,WAAW,CAAC,wBAAwB,CAAC,CACrChB,YAAY,EAAE;IAEjB,OAAOR,OAAO,CAAC3F,GAAG,EAAEoE,aAAa,EAAE8C,gBAAgB,CAAC;GACrD,sCAEF,CACF;EAEDH,kBAAkB,CAChB,IAAIC,SAAS,CAAC,oBAAoB,EAAEI,eAAe,EAAwB,uCAC5E;EAEDC,eAAe,CAACC,IAAI,EAAEC,OAAO,CAAC;;EAE9BF,eAAe,CAACC,IAAI,EAAEC,OAAO,EAAE,SAAkB,CAAC;EAElD,SAASH,eAAeA,CACtBH,SAA6B;IAE7B,IAAI;MACF,MAAMO,SAAS,GAAGP,SAAS,CAACE,WAAW,CAAC7O,cAAc,CAAC,CAAC6N,YAAY,EAAE;MACtE,OAAO;QACLjE,QAAQ,EAAEA,CACRE,SAAiB,EACjBC,WAAwC,EACxClC,OAA8B,KAC3B+B,QAAQ,CAACsF,SAAS,EAAEpF,SAAS,EAAEC,WAAW,EAAElC,OAAO;OACzD;KACF,CAAC,OAAOnG,CAAC,EAAE;MACV,MAAMjB,aAAa,CAACM,MAAM,CAA8C;QACtEoO,MAAM,EAAEzN;MACT,EAAC;;;AAGR;AAEA8M,iBAAiB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
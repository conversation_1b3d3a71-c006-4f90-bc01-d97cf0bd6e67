import React from 'react';

const FirebaseStatus = () => {
  const config = {
    apiKey: process.env.REACT_APP_FIREBASE_API_KEY,
    authDomain: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.REACT_APP_FIREBASE_PROJECT_ID,
    storageBucket: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.REACT_APP_FIREBASE_APP_ID,
    measurementId: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID
  };

  const configItems = [
    { key: 'API Key', value: config.apiKey, required: true },
    { key: 'Auth Domain', value: config.authDomain, required: true },
    { key: 'Project ID', value: config.projectId, required: true },
    { key: 'Storage Bucket', value: config.storageBucket, required: true },
    { key: 'Messaging Sender ID', value: config.messagingSenderId, required: true },
    { key: 'App ID', value: config.appId, required: true },
    { key: 'Measurement ID', value: config.measurementId, required: false },
  ];

  const allRequired = configItems.filter(item => item.required).every(item => item.value);

  return (
    <div className="bg-white border rounded-lg p-4 mb-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center">
        <span className="mr-2">🔧</span>
        Firebase Configuration Status
      </h3>
      
      <div className="space-y-2">
        {configItems.map((item) => (
          <div key={item.key} className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{item.key}:</span>
            <span className={`text-sm font-medium ${
              item.value 
                ? 'text-green-600' 
                : item.required 
                  ? 'text-red-600' 
                  : 'text-yellow-600'
            }`}>
              {item.value ? '✓ Configured' : item.required ? '✗ Missing' : '⚠ Optional'}
            </span>
          </div>
        ))}
      </div>

      <div className={`mt-4 p-3 rounded-lg ${
        allRequired 
          ? 'bg-green-50 border border-green-200' 
          : 'bg-red-50 border border-red-200'
      }`}>
        <div className={`text-sm font-medium ${
          allRequired ? 'text-green-800' : 'text-red-800'
        }`}>
          {allRequired 
            ? '✅ Firebase configuration is complete' 
            : '❌ Firebase configuration is incomplete'
          }
        </div>
        {!allRequired && (
          <div className="text-red-700 text-xs mt-1">
            Please check your .env file and ensure all required Firebase configuration values are set.
          </div>
        )}
      </div>

      {allRequired && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="text-blue-800 text-sm font-medium">
            📋 Next Steps for Google Sign-In:
          </div>
          <ol className="text-blue-700 text-xs mt-1 list-decimal list-inside space-y-1">
            <li>Go to Firebase Console → Authentication → Sign-in method</li>
            <li>Enable Google provider</li>
            <li>Add your support email</li>
            <li>Ensure localhost is in authorized domains</li>
          </ol>
        </div>
      )}
    </div>
  );
};

export default FirebaseStatus;

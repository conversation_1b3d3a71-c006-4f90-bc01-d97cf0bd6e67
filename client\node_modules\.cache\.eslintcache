[{"C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Dashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Home.js": "6", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\Navbar.js": "8", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\FoodListing.js": "9", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\FoodListingCard.js": "10", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\LoadingSpinner.js": "11", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firebase.js": "12", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\authService.js": "13", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\foodService.js": "14", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\InitializeData.js": "15", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firestoreSetup.js": "16"}, {"size": 535, "mtime": 1754920552403, "results": "17", "hashOfConfig": "18"}, {"size": 1241, "mtime": 1754920734618, "results": "19", "hashOfConfig": "18"}, {"size": 362, "mtime": 1754920552493, "results": "20", "hashOfConfig": "18"}, {"size": 485, "mtime": 1754920875108, "results": "21", "hashOfConfig": "18"}, {"size": 5246, "mtime": 1754927021848, "results": "22", "hashOfConfig": "18"}, {"size": 3222, "mtime": 1754920757504, "results": "23", "hashOfConfig": "18"}, {"size": 4800, "mtime": 1754926857147, "results": "24", "hashOfConfig": "18"}, {"size": 5487, "mtime": 1754926877017, "results": "25", "hashOfConfig": "18"}, {"size": 8939, "mtime": 1754926934616, "results": "26", "hashOfConfig": "18"}, {"size": 6385, "mtime": 1754926986163, "results": "27", "hashOfConfig": "18"}, {"size": 528, "mtime": 1754920882592, "results": "28", "hashOfConfig": "18"}, {"size": 2359, "mtime": 1754926728912, "results": "29", "hashOfConfig": "18"}, {"size": 4324, "mtime": 1754926830747, "results": "30", "hashOfConfig": "18"}, {"size": 6567, "mtime": 1754926907373, "results": "31", "hashOfConfig": "18"}, {"size": 2277, "mtime": 1754927001409, "results": "32", "hashOfConfig": "18"}, {"size": 3951, "mtime": 1754926804703, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "72dtif", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\FoodListing.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\FoodListingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firebase.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\authService.js", ["82"], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\foodService.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\InitializeData.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firestoreSetup.js", [], [], {"ruleId": "83", "severity": 1, "message": "84", "line": 5, "column": 3, "nodeType": "85", "messageId": "86", "endLine": 5, "endColumn": 21}, "no-unused-vars", "'GoogleAuthProvider' is defined but never used.", "Identifier", "unusedVar"]
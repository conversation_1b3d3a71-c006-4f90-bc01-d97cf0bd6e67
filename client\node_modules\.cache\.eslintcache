[{"C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Dashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Home.js": "6", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\Navbar.js": "8", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\FoodListing.js": "9", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\FoodListingCard.js": "10", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\LoadingSpinner.js": "11", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firebase.js": "12", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\authService.js": "13", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\foodService.js": "14", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\InitializeData.js": "15", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firestoreSetup.js": "16", "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\FirebaseStatus.js": "17"}, {"size": 535, "mtime": 1754920552403, "results": "18", "hashOfConfig": "19"}, {"size": 1241, "mtime": 1754920734618, "results": "20", "hashOfConfig": "19"}, {"size": 362, "mtime": 1754920552493, "results": "21", "hashOfConfig": "19"}, {"size": 485, "mtime": 1754920875108, "results": "22", "hashOfConfig": "19"}, {"size": 5246, "mtime": 1754927021848, "results": "23", "hashOfConfig": "19"}, {"size": 3222, "mtime": 1754920757504, "results": "24", "hashOfConfig": "19"}, {"size": 4929, "mtime": 1754927436865, "results": "25", "hashOfConfig": "19"}, {"size": 5487, "mtime": 1754926877017, "results": "26", "hashOfConfig": "19"}, {"size": 8939, "mtime": 1754926934616, "results": "27", "hashOfConfig": "19"}, {"size": 6385, "mtime": 1754926986163, "results": "28", "hashOfConfig": "19"}, {"size": 528, "mtime": 1754920882592, "results": "29", "hashOfConfig": "19"}, {"size": 2870, "mtime": 1754927372850, "results": "30", "hashOfConfig": "19"}, {"size": 4880, "mtime": 1754927387774, "results": "31", "hashOfConfig": "19"}, {"size": 6567, "mtime": 1754926907373, "results": "32", "hashOfConfig": "19"}, {"size": 2277, "mtime": 1754927001409, "results": "33", "hashOfConfig": "19"}, {"size": 3951, "mtime": 1754926804703, "results": "34", "hashOfConfig": "19"}, {"size": 3311, "mtime": 1754927416051, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "72dtif", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\pages\\FoodListing.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\FoodListingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firebase.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\authService.js", ["87"], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\foodService.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\InitializeData.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\services\\firestoreSetup.js", [], [], "C:\\Users\\<USER>\\Desktop\\ZeroWaste\\client\\src\\components\\FirebaseStatus.js", [], [], {"ruleId": "88", "severity": 1, "message": "89", "line": 5, "column": 3, "nodeType": "90", "messageId": "91", "endLine": 5, "endColumn": 21}, "no-unused-vars", "'GoogleAuthProvider' is defined but never used.", "Identifier", "unusedVar"]
import { 
  signInWithPopup, 
  signOut, 
  onAuthStateChanged,
  GoogleAuthProvider 
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  serverTimestamp 
} from 'firebase/firestore';
import { auth, db, googleProvider } from './firebase';
import { requestNotificationPermission } from './firebase';

// Enhanced Google provider configuration
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Sign in with Google
export const signInWithGoogle = async (selectedRole) => {
  try {
    if (!selectedRole) {
      throw new Error('Please select a role before signing in');
    }

    const result = await signInWithPopup(auth, googleProvider);
    const user = result.user;

    // Check if user already exists in Firestore
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);

    if (!userDoc.exists()) {
      // Request notification permission and get FCM token
      const fcmToken = await requestNotificationPermission();

      // Create new user document
      await setDoc(userDocRef, {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        role: selectedRole,
        createdAt: serverTimestamp(),
        lastLoginAt: serverTimestamp(),
        fcmToken: fcmToken,
        preferences: {
          notifications: true,
          emailUpdates: false,
        },
        stats: {
          foodListed: 0,
          foodClaimed: 0,
          impactScore: 0,
        }
      });

      console.log('New user created:', user.uid);
    } else {
      // Update existing user's last login and FCM token
      const fcmToken = await requestNotificationPermission();
      await updateDoc(userDocRef, {
        lastLoginAt: serverTimestamp(),
        fcmToken: fcmToken,
      });

      console.log('Existing user signed in:', user.uid);
    }

    return {
      success: true,
      user: user,
      isNewUser: !userDoc.exists()
    };
  } catch (error) {
    console.error('Error signing in with Google:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Sign out
export const signOutUser = async () => {
  try {
    await signOut(auth);
    console.log('User signed out successfully');
    return { success: true };
  } catch (error) {
    console.error('Error signing out:', error);
    return { success: false, error: error.message };
  }
};

// Get current user data from Firestore
export const getCurrentUserData = async (uid) => {
  try {
    const userDocRef = doc(db, 'users', uid);
    const userDoc = await getDoc(userDocRef);
    
    if (userDoc.exists()) {
      return {
        success: true,
        userData: userDoc.data()
      };
    } else {
      return {
        success: false,
        error: 'User data not found'
      };
    }
  } catch (error) {
    console.error('Error getting user data:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Update user profile
export const updateUserProfile = async (uid, updates) => {
  try {
    const userDocRef = doc(db, 'users', uid);
    await updateDoc(userDocRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
    
    return { success: true };
  } catch (error) {
    console.error('Error updating user profile:', error);
    return { success: false, error: error.message };
  }
};

// Auth state observer
export const onAuthStateChange = (callback) => {
  return onAuthStateChanged(auth, callback);
};

// Check if user has specific role
export const hasRole = async (uid, requiredRole) => {
  try {
    const userData = await getCurrentUserData(uid);
    if (userData.success) {
      return userData.userData.role === requiredRole;
    }
    return false;
  } catch (error) {
    console.error('Error checking user role:', error);
    return false;
  }
};

// Check if user can list food (canteen staff, admin)
export const canListFood = async (uid) => {
  try {
    const userData = await getCurrentUserData(uid);
    if (userData.success) {
      const role = userData.userData.role;
      return role === 'canteen_staff' || role === 'admin';
    }
    return false;
  } catch (error) {
    console.error('Error checking food listing permission:', error);
    return false;
  }
};

import { collection, doc, setDoc } from 'firebase/firestore';
import { db } from './firebase';

// Initialize Firestore collections with sample data and proper structure
export const initializeFirestore = async () => {
  try {
    console.log('Initializing Firestore collections...');

    // Create sample food listing
    const sampleFoodListing = {
      name: 'Vegetable Biryani',
      description: 'Freshly prepared vegetable biryani with aromatic spices',
      quantity: 20,
      unit: 'servings',
      freshnessStatus: 'fresh',
      expiryHours: 4,
      expiryTime: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
      location: 'Main Canteen',
      contactInfo: '+91 9876543210',
      allergens: 'Vegetarian, Contains nuts',
      imageUrl: null,
      status: 'available',
      listedBy: {
        uid: 'sample-user-id',
        name: 'Main Canteen Staff',
        email: '<EMAIL>',
      },
      claimedBy: null,
      claimedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add sample food listing
    await setDoc(doc(collection(db, 'foodListings'), 'sample-listing'), sampleFoodListing);

    // Create sample user document structure
    const sampleUser = {
      uid: 'sample-user-id',
      email: '<EMAIL>',
      displayName: 'Main Canteen Staff',
      photoURL: null,
      role: 'canteen_staff',
      createdAt: new Date(),
      fcmToken: null,
      preferences: {
        notifications: true,
        emailUpdates: false,
      },
      stats: {
        foodListed: 1,
        foodClaimed: 0,
        impactScore: 100,
      }
    };

    // Add sample user
    await setDoc(doc(collection(db, 'users'), 'sample-user-id'), sampleUser);

    // Create sample impact data
    const sampleImpact = {
      totalFoodSaved: 1250,
      totalPeopleServed: 500,
      co2Saved: 2.5, // in tons
      waterSaved: 1200, // in liters
      lastUpdated: new Date(),
    };

    // Add sample impact data
    await setDoc(doc(collection(db, 'impact'), 'global-stats'), sampleImpact);

    console.log('Firestore collections initialized successfully!');
    return true;
  } catch (error) {
    console.error('Error initializing Firestore:', error);
    return false;
  }
};

// Firestore Security Rules (to be added in Firebase Console)
export const firestoreSecurityRules = `
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null; // Allow reading other users for display purposes
    }
    
    // Food listings rules
    match /foodListings/{listingId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
                   request.auth.uid == resource.data.listedBy.uid;
      allow update: if request.auth != null && (
        // Owner can update their listing
        request.auth.uid == resource.data.listedBy.uid ||
        // Users can claim food (update claimedBy and status)
        (resource.data.status == 'available' && 
         request.resource.data.status == 'claimed' &&
         request.resource.data.claimedBy.uid == request.auth.uid)
      );
      allow delete: if request.auth != null && 
                   request.auth.uid == resource.data.listedBy.uid;
    }
    
    // Impact data - read only for users, write for cloud functions
    match /impact/{document} {
      allow read: if request.auth != null;
      allow write: if false; // Only cloud functions can write
    }
    
    // Claims collection for tracking
    match /claims/{claimId} {
      allow read, write: if request.auth != null && (
        request.auth.uid == resource.data.claimedBy.uid ||
        request.auth.uid == resource.data.listedBy.uid
      );
    }
  }
}
`;

import React, { useState } from 'react';
import { initializeFirestore } from '../services/firestoreSetup';

const InitializeData = () => {
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);

  const handleInitialize = async () => {
    setLoading(true);
    try {
      const success = await initializeFirestore();
      if (success) {
        setInitialized(true);
        alert('Sample data initialized successfully!');
      } else {
        alert('Failed to initialize sample data. Check console for errors.');
      }
    } catch (error) {
      console.error('Error initializing data:', error);
      alert('Error initializing data. Check console for errors.');
    } finally {
      setLoading(false);
    }
  };

  if (initialized) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <div className="flex items-center">
          <div className="text-green-600 mr-3">✅</div>
          <div>
            <h3 className="text-green-800 font-semibold">Sample Data Initialized</h3>
            <p className="text-green-700 text-sm">
              Sample food listings and user data have been added to the database.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-blue-800 font-semibold">Initialize Sample Data</h3>
          <p className="text-blue-700 text-sm">
            Add sample food listings and user data to test the application.
          </p>
        </div>
        <button
          onClick={handleInitialize}
          disabled={loading}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Initializing...
            </div>
          ) : (
            'Initialize Data'
          )}
        </button>
      </div>
    </div>
  );
};

export default InitializeData;
